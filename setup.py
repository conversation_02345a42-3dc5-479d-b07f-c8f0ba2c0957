#!/usr/bin/env python3
"""
Setup script for the Atlassian Client

This script helps set up the environment and validates the installation.
"""

import subprocess
import sys
import os

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 6):
        print("❌ Python 3.6 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """Install required packages"""
    print("\n📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False
    except FileNotFoundError:
        print("❌ requirements.txt not found")
        return False

def check_configuration():
    """Check if configuration is properly set up"""
    print("\n⚙️ Checking configuration...")
    
    try:
        from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS
        
        required_keys = [
            'CONFLUENCE_URL', 'CONFLUENCE_USERNAME', 'CONFLUENCE_API_TOKEN',
            'JIRA_URL', 'JIRA_USERNAME', 'JIRA_API_TOKEN'
        ]
        
        missing_keys = [key for key in required_keys if key not in ATLASSIAN_CONFIG]
        
        if missing_keys:
            print(f"❌ Missing configuration keys: {missing_keys}")
            print("Please update config.py with your Atlassian credentials")
            return False
        
        print("✅ Configuration file is properly set up")
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import configuration: {e}")
        return False

def test_import():
    """Test if the client can be imported"""
    print("\n📚 Testing client import...")
    
    try:
        from client import AtlassianClient, ConfluenceAPI, JiraAPI
        print("✅ Client modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import client: {e}")
        return False

def run_connection_test():
    """Run a quick connection test"""
    print("\n🔗 Testing connection...")
    
    try:
        result = subprocess.run(
            [sys.executable, 'main.py', 'test'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ Connection test passed")
            return True
        else:
            print("❌ Connection test failed")
            print("Output:", result.stdout)
            print("Error:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Connection test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running connection test: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Atlassian Client Setup")
    print("=" * 50)
    
    steps = [
        ("Python Version Check", check_python_version),
        ("Install Dependencies", install_dependencies),
        ("Check Configuration", check_configuration),
        ("Test Import", test_import),
        ("Connection Test", run_connection_test)
    ]
    
    all_passed = True
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            all_passed = False
            break
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Use 'python main.py' for command-line interface")
        print("2. Check 'examples/example_usage.py' for programming examples")
        print("3. Run 'python run_tests.py' for comprehensive testing")
        print("4. Read README.md for detailed documentation")
    else:
        print("❌ Setup failed. Please fix the issues above and try again.")
        print("\nCommon solutions:")
        print("1. Update config.py with valid credentials")
        print("2. Ensure network connectivity to Atlassian servers")
        print("3. Check if Personal Access Tokens are valid")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
