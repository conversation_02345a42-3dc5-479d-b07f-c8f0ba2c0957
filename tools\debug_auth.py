#!/usr/bin/env python3
"""
Authentication debugging tool
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import requests
import base64
from config import ATLASSIAN_CONFIG

def test_basic_auth():
    """Test basic authentication"""
    print("Testing Basic Authentication...")
    
    username = ATLASSIAN_CONFIG['CONFLUENCE_USERNAME']
    token = ATLASSIAN_CONFIG['CONFLUENCE_API_TOKEN']
    
    credentials = f"{username}:{token}"
    encoded = base64.b64encode(credentials.encode()).decode()
    auth_header = f"Basic {encoded}"
    
    headers = {
        'Authorization': auth_header,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    # Test Confluence
    confluence_url = f"{ATLASSIAN_CONFIG['CONFLUENCE_URL']}/rest/api/space?limit=1"
    try:
        response = requests.get(confluence_url, headers=headers, verify=False, timeout=30)
        print(f"Confluence Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Confluence Basic Auth: SUCCESS")
        else:
            print(f"❌ Confluence Basic Auth: FAILED - {response.text[:100]}")
    except Exception as e:
        print(f"❌ Confluence Basic Auth Error: {e}")
    
    # Test Jira
    jira_url = f"{ATLASSIAN_CONFIG['JIRA_URL']}/rest/api/2/serverInfo"
    try:
        response = requests.get(jira_url, headers=headers, verify=False, timeout=30)
        print(f"Jira Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Jira Basic Auth: SUCCESS")
        else:
            print(f"❌ Jira Basic Auth: FAILED - {response.text[:100]}")
    except Exception as e:
        print(f"❌ Jira Basic Auth Error: {e}")

def test_session_auth():
    """Test session-based authentication"""
    print("\nTesting Session Authentication...")
    
    session = requests.Session()
    session.verify = False
    
    username = ATLASSIAN_CONFIG['CONFLUENCE_USERNAME']
    token = ATLASSIAN_CONFIG['CONFLUENCE_API_TOKEN']
    
    # Test Confluence session
    login_url = f"{ATLASSIAN_CONFIG['CONFLUENCE_URL']}/rest/auth/1/session"
    login_data = {"username": username, "password": token}
    
    try:
        response = session.post(login_url, json=login_data, timeout=30)
        print(f"Confluence Session Login: {response.status_code}")
        if response.status_code == 200:
            print("✅ Confluence Session: SUCCESS")
            
            # Test status endpoint
            status_url = f"{ATLASSIAN_CONFIG['CONFLUENCE_URL']}/status"
            status_response = session.get(status_url, timeout=10)
            print(f"Confluence Status Endpoint: {status_response.status_code}")
        else:
            print(f"❌ Confluence Session: FAILED - {response.text[:100]}")
    except Exception as e:
        print(f"❌ Confluence Session Error: {e}")

if __name__ == "__main__":
    print("🔍 Authentication Debug Tool")
    print("="*50)
    test_basic_auth()
    test_session_auth()
