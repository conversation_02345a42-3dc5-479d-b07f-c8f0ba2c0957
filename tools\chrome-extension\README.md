# 🍪 Simplified Atlassian Cookie Extractor

## 🎯 **What It Does**

This Chrome extension provides a **simple, focused solution** for extracting Atlassian cookies and setting up your environment:

1. **Extract Cookies**: One-click extraction from Confluence and Jira
2. **Download .bat File**: Generates a Windows batch script
3. **Set Environment**: Run the .bat file to configure environment variables
4. **Ready to Use**: Your Python scripts work immediately

## 🚀 **Quick Start**

### Install Extension:
1. Open Chrome → `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" → Select `chrome-extension` folder

### Use Extension:
1. **Login** to Confluence/Jira with 2FA
2. **Click extension icon** in Chrome toolbar
3. **Extract cookies** (click the extract buttons)
4. **Download .bat file** (click "📜 Download .bat File")
5. **Run the .bat file** to set environment variables

### Test Your Setup:
```bash
# The .bat file sets these automatically:
# set CONFLUENCE_BROWSER_COOKIE=your_extracted_cookie
# set JIRA_BROWSER_COOKIE=your_extracted_cookie

# Test integration:
python examples/complete_integration_test.py
```

## 📋 **Features**

### ✅ **What's Included:**
- Cookie extraction for Confluence and Jira
- Automatic .bat file generation
- Environment variable setup
- Cookie validation and storage
- Clean, simple interface

### ❌ **What's Removed:**
- Complex configuration options
- Multiple export formats
- Local server integration
- PowerShell scripts
- Copy to clipboard features

## 🎯 **Perfect For:**

- **Daily development work**
- **Quick environment setup**
- **Windows users** (generates .bat files)
- **Simple workflows**
- **One-time setup** scenarios

## 📁 **Generated .bat File Example:**

```batch
@echo off
echo ================================================
echo  Atlassian Cookie Environment Setup
echo ================================================

echo Setting Confluence cookie...
set CONFLUENCE_BROWSER_COOKIE=826908A1CFAD61EB7F4C1FC7B5BDE952
echo ✅ Confluence cookie set

echo Setting Jira cookie...
set JIRA_BROWSER_COOKIE=your_jira_cookie_here
echo ✅ Jira cookie set

echo ================================================
echo  Setup Complete!
echo ================================================

echo You can now run your Python scripts:
echo   python examples/complete_integration_test.py
echo   python examples/confluence_with_browser_cookie.py

echo Environment variables are set for this session.
pause
```

## 🔧 **How It Works:**

1. **Chrome Cookies API**: Uses `chrome.cookies.get()` to extract session cookies
2. **Batch Script Generation**: Creates Windows batch commands to set environment variables
3. **File Download**: Uses browser download API to save the .bat file
4. **Environment Setup**: User runs the .bat file to configure their session

## 🎉 **Benefits:**

- **Simple**: Just extract and download
- **Fast**: One-click cookie extraction
- **Reliable**: Uses official Chrome APIs
- **Secure**: No network requests, local-only processing
- **Automated**: .bat file handles environment setup
- **Focused**: Does one thing very well

## 🔄 **Workflow:**

```
Login to Confluence/Jira
         ↓
Click Extension Icon
         ↓
Extract Cookies
         ↓
Download .bat File
         ↓
Run .bat File
         ↓
Environment Configured!
         ↓
Run Python Scripts
```

## 🎯 **Use Cases:**

- **Development Setup**: Quick environment configuration
- **Testing**: Set up test environments rapidly
- **Automation**: Part of larger setup scripts
- **Team Onboarding**: Simple setup for new team members
- **CI/CD**: Generate environment files for pipelines

This simplified extension focuses on **doing one thing extremely well**: extracting cookies and providing an easy way to set up your environment for Atlassian integration! 🚀
