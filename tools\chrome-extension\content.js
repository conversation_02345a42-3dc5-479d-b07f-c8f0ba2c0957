// Content script for Atlassian Cookie Extractor
// Runs on Confluence and Jira pages to detect login state

// Check if user is logged in to the current Atlassian service
function checkLoginStatus() {
    const hostname = window.location.hostname;
    
    if (hostname.includes('confluence')) {
        return checkConfluenceLogin();
    } else if (hostname.includes('jira')) {
        return checkJiraLogin();
    }
    
    return false;
}

function checkConfluenceLogin() {
    // Look for elements that indicate logged-in state in Confluence
    const indicators = [
        '#header',
        '.aui-header',
        '#user-menu-link',
        '[data-test-id="user-avatar"]'
    ];
    
    return indicators.some(selector => document.querySelector(selector) !== null);
}

function checkJiraLogin() {
    // Look for elements that indicate logged-in state in Jira
    const indicators = [
        '#header',
        '.aui-header',
        '#user-options',
        '[data-test-id="user-avatar"]'
    ];
    
    return indicators.some(selector => document.querySelector(selector) !== null);
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'checkLoginStatus') {
        const isLoggedIn = checkLoginStatus();
        sendResponse({ isLoggedIn });
    }
});

// Notify background script when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        chrome.runtime.sendMessage({
            action: 'pageLoaded',
            url: window.location.href,
            isLoggedIn: checkLoginStatus()
        });
    });
} else {
    chrome.runtime.sendMessage({
        action: 'pageLoaded',
        url: window.location.href,
        isLoggedIn: checkLoginStatus()
    });
}
