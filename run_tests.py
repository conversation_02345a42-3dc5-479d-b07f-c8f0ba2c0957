#!/usr/bin/env python3
"""
Test runner for the Atlassian Client

This script runs all tests from the root directory for convenience.
"""

import subprocess
import sys
import os

def run_test(test_name, test_file):
    """Run a specific test and return success status"""
    print(f"\n{'='*60}")
    print(f"Running {test_name}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            [sys.executable, os.path.join('tests', test_file)],
            cwd=os.path.dirname(__file__),
            capture_output=False,
            text=True
        )
        
        success = result.returncode == 0
        print(f"\n{test_name}: {'PASSED' if success else 'FAILED'}")
        return success
        
    except Exception as e:
        print(f"Error running {test_name}: {e}")
        return False

def main():
    """Run all tests"""
    print("Atlassian Client Test Suite")
    print("Running all tests...")
    
    tests = [
        ("Authentication Test", "test_auth.py"),
        ("Personal Access Token Test", "test_pat.py"),
        ("Full Client Test", "test_client.py")
    ]
    
    results = []
    
    for test_name, test_file in tests:
        success = run_test(test_name, test_file)
        results.append((test_name, success))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "PASSED" if success else "FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The client is ready to use.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
