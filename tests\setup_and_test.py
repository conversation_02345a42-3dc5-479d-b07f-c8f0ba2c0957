#!/usr/bin/env python3
"""
Setup and test script for the Atlassian Client

This script helps set up the environment and run initial tests.
"""

import subprocess
import sys
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 6):
        logger.error("Python 3.6 or higher is required")
        return False
    
    logger.info(f"Python version: {sys.version}")
    return True


def install_requirements():
    """Install required packages"""
    logger.info("Installing requirements...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        logger.info("Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install requirements: {e}")
        return False
    except FileNotFoundError:
        logger.error("requirements.txt not found")
        return False


def check_files():
    """Check if all required files are present"""
    required_files = [
        'atlassian_client.py',
        'example_usage.py',
        'test_client.py',
        'requirements.txt',
        'README.md'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"Missing files: {', '.join(missing_files)}")
        return False
    
    logger.info("All required files are present")
    return True


def run_tests():
    """Run the test suite"""
    logger.info("Running test suite...")
    
    try:
        result = subprocess.run([sys.executable, 'test_client.py'], 
                              capture_output=True, text=True)
        
        # Print test output
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            logger.info("All tests passed!")
            return True
        else:
            logger.warning("Some tests failed. Check the output above.")
            return False
            
    except Exception as e:
        logger.error(f"Failed to run tests: {e}")
        return False


def main():
    """Main setup and test function"""
    logger.info("Atlassian Client Setup and Test")
    logger.info("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Check files
    if not check_files():
        return 1
    
    # Install requirements
    if not install_requirements():
        return 1
    
    # Run tests
    logger.info("\nRunning tests to validate the setup...")
    test_success = run_tests()
    
    logger.info("\n" + "=" * 40)
    if test_success:
        logger.info("✅ Setup completed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. Review the test results above")
        logger.info("2. Check example_usage.py for usage examples")
        logger.info("3. Read README.md for detailed documentation")
        logger.info("4. Start using the client in your projects!")
    else:
        logger.info("⚠️  Setup completed with some test failures")
        logger.info("\nNext steps:")
        logger.info("1. Review the test failures above")
        logger.info("2. Check your Atlassian configuration")
        logger.info("3. Verify network connectivity to your Atlassian servers")
        logger.info("4. Contact your administrator if authentication issues persist")
    
    return 0 if test_success else 1


if __name__ == "__main__":
    sys.exit(main())
