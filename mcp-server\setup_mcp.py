#!/usr/bin/env python3
"""
Setup script for the Atlassian MCP Server

This script helps set up the MCP server environment and validates the installation.
"""

import subprocess
import sys
import os
import json
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required for MCP")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_mcp_dependencies():
    """Install MCP-specific dependencies"""
    print("\n📦 Installing MCP dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✅ MCP dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install MCP dependencies: {e}")
        return False
    except FileNotFoundError:
        print("❌ requirements.txt not found")
        return False

def check_parent_client():
    """Check if the parent Atlassian client is available"""
    print("\n🔍 Checking parent Atlassian client...")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
        from client import AtlassianClient
        from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS
        
        print("✅ Parent Atlassian client is available")
        return True
    except ImportError as e:
        print(f"❌ Cannot import parent client: {e}")
        print("Make sure the parent Atlassian client is properly set up")
        return False

def test_extended_client():
    """Test the extended client functionality"""
    print("\n🧪 Testing extended client...")
    
    try:
        from extended_atlassian_client import ExtendedAtlassianClient
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
        from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS
        
        client = ExtendedAtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        connection_status = client.test_connection()
        
        print(f"Confluence: {'✅ Connected' if connection_status['confluence'] else '❌ Failed'}")
        print(f"Jira: {'✅ Connected' if connection_status['jira'] else '❌ Failed'}")
        
        return connection_status['confluence'] or connection_status['jira']
    except Exception as e:
        print(f"❌ Extended client test failed: {e}")
        return False

def test_mcp_server():
    """Test the MCP server functionality"""
    print("\n🖥️ Testing MCP server...")
    
    try:
        result = subprocess.run(
            [sys.executable, 'test_mcp_server.py'],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print("✅ MCP server test passed")
            return True
        else:
            print("❌ MCP server test failed")
            print("Output:", result.stdout[-500:])  # Last 500 chars
            print("Error:", result.stderr[-500:])
            return False
    except subprocess.TimeoutExpired:
        print("❌ MCP server test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running MCP server test: {e}")
        return False

def generate_mcp_config():
    """Generate MCP client configuration with environment variables"""
    print("\n⚙️ Generating MCP client configuration...")

    try:
        # Get absolute path to the server
        server_path = os.path.abspath('server.py')
        parent_path = os.path.abspath('..')

        # Try to get existing config for defaults
        try:
            sys.path.insert(0, parent_path)
            from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

            config_with_env = {
                "mcpServers": {
                    "atlassian": {
                        "command": "python",
                        "args": [server_path],
                        "env": {
                            "PYTHONPATH": parent_path,
                            "CONFLUENCE_URL": ATLASSIAN_CONFIG.get('CONFLUENCE_URL', 'https://your-confluence-server.com'),
                            "CONFLUENCE_USERNAME": ATLASSIAN_CONFIG.get('CONFLUENCE_USERNAME', '<EMAIL>'),
                            "CONFLUENCE_API_TOKEN": "your_confluence_personal_access_token",
                            "JIRA_URL": ATLASSIAN_CONFIG.get('JIRA_URL', 'https://your-jira-server.com'),
                            "JIRA_USERNAME": ATLASSIAN_CONFIG.get('JIRA_USERNAME', '<EMAIL>'),
                            "JIRA_API_TOKEN": "your_jira_personal_access_token",
                            "ATLASSIAN_AUTH_METHOD": CLIENT_SETTINGS.get('auth_method', 'bearer'),
                            "ATLASSIAN_VERIFY_SSL": str(CLIENT_SETTINGS.get('verify_ssl', True)).lower(),
                            "ATLASSIAN_TIMEOUT": str(CLIENT_SETTINGS.get('timeout', 30)),
                            "ATLASSIAN_MAX_RETRIES": str(CLIENT_SETTINGS.get('max_retries', 3))
                        }
                    }
                }
            }

            # Write environment-based configuration
            with open('mcp_config_env.json', 'w') as f:
                json.dump(config_with_env, f, indent=2)

            print("✅ Environment-based MCP configuration generated: mcp_config_env.json")
            print("⚠️  Remember to replace 'your_*_personal_access_token' with actual tokens!")

        except ImportError:
            print("⚠️  Could not import config.py, generating template only")

        # Also generate the basic config (fallback to file-based config)
        basic_config = {
            "mcpServers": {
                "atlassian": {
                    "command": "python",
                    "args": [server_path],
                    "env": {
                        "PYTHONPATH": parent_path
                    }
                }
            }
        }

        with open('mcp_config_basic.json', 'w') as f:
            json.dump(basic_config, f, indent=2)

        print("✅ Basic MCP configuration generated: mcp_config_basic.json")
        print(f"Server path: {server_path}")
        print(f"Python path: {parent_path}")

        return True
    except Exception as e:
        print(f"❌ Failed to generate MCP config: {e}")
        return False

def show_usage_instructions():
    """Show usage instructions"""
    print("\n📖 Usage Instructions:")
    print("=" * 50)

    print("\n1. **Configuration Options:**")
    print("   a) Environment-based (Recommended): Use 'mcp_config_env.json'")
    print("      - All credentials in MCP client environment")
    print("      - No config.py dependency")
    print("      - More secure and portable")
    print("   b) File-based (Fallback): Use 'mcp_config_basic.json'")
    print("      - Uses existing config.py file")
    print("      - Requires config.py in parent directory")

    print("\n2. **Environment Variables (for option a):**")
    print("   Required:")
    print("   - CONFLUENCE_URL, CONFLUENCE_USERNAME, CONFLUENCE_API_TOKEN")
    print("   - JIRA_URL, JIRA_USERNAME, JIRA_API_TOKEN")
    print("   Optional:")
    print("   - ATLASSIAN_AUTH_METHOD (default: bearer)")
    print("   - ATLASSIAN_VERIFY_SSL (default: true)")
    print("   - ATLASSIAN_TIMEOUT (default: 30)")
    print("   - ATLASSIAN_MAX_RETRIES (default: 3)")

    print("\n3. **Add to your MCP client:**")
    print("   Copy the contents of your chosen config file to your MCP client")

    print("\n4. **Test the server:**")
    print("   python test_mcp_server.py")

    print("\n5. **Available resources:**")
    print("   - atlassian://system/info")
    print("   - atlassian://confluence/spaces")
    print("   - atlassian://jira/projects")
    print("   - And many more...")

    print("\n6. **Available tools:**")
    print("   - confluence_search, confluence_create_page")
    print("   - jira_search_issues, jira_create_issue")
    print("   - And 16+ more tools...")

def main():
    """Main setup function"""
    print("🚀 Atlassian MCP Server Setup")
    print("=" * 50)
    
    steps = [
        ("Python Version Check", check_python_version),
        ("Install MCP Dependencies", install_mcp_dependencies),
        ("Check Parent Client", check_parent_client),
        ("Test Extended Client", test_extended_client),
        ("Test MCP Server", test_mcp_server),
        ("Generate MCP Config", generate_mcp_config)
    ]
    
    all_passed = True
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            all_passed = False
            if step_name in ["Python Version Check", "Install MCP Dependencies", "Check Parent Client"]:
                # Critical failures - stop here
                break
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 MCP Server setup completed successfully!")
        show_usage_instructions()
    else:
        print("❌ Setup failed. Please fix the issues above and try again.")
        print("\nCommon solutions:")
        print("1. Ensure the parent Atlassian client is set up (run ../setup.py)")
        print("2. Update config.py with valid credentials")
        print("3. Check network connectivity to Atlassian servers")
        print("4. Install required Python packages")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
