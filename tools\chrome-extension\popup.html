<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .service-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        
        .extract-btn {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }
        
        .extract-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .extract-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .status {
            font-size: 12px;
            padding: 5px 8px;
            border-radius: 4px;
            margin-top: 5px;
            text-align: center;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid rgba(244, 67, 54, 0.5);
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.3);
            border: 1px solid rgba(33, 150, 243, 0.5);
        }
        
        .cookie-display {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            padding: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            word-break: break-all;
            margin-top: 8px;
            max-height: 60px;
            overflow-y: auto;
        }
        
        .actions {
            margin-top: 15px;
            text-align: center;
        }
        
        .copy-btn, .save-btn {
            padding: 8px 16px;
            margin: 0 5px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover, .save-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 11px;
            opacity: 0.7;
        }

        .cookie-item {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
        }

        .cookie-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .cookie-label {
            font-weight: 600;
            font-size: 13px;
            display: flex;
            align-items: center;
        }

        .service-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }

        .copy-icon {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            padding: 4px 8px;
            transition: all 0.3s ease;
        }

        .copy-icon:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .cookie-value {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            padding: 8px;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            word-break: break-all;
            line-height: 1.4;
            max-height: 80px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🍪 Atlassian Cookie Extractor</h1>
        <p>Extract cookies and download .bat setup file</p>
    </div>
    
    <div class="service-section">
        <button id="extractAll" class="extract-btn">Extract All Cookies</button>
        
        <div id="cookieResults" style="display: none; margin-top: 15px;">
            <div class="cookie-item" id="confluenceResult" style="display: none;">
                <div class="cookie-header">
                    <span class="cookie-label">
                        <img src="icons/confluence.png" alt="Confluence" class="service-icon">
                        Confluence Cookie:
                    </span>
                    <button class="copy-icon" id="copyConfluence" title="Copy to clipboard">📋</button>
                </div>
                <div class="cookie-value" id="confluenceValue"></div>
            </div>

            <div class="cookie-item" id="jiraResult" style="display: none;">
                <div class="cookie-header">
                    <span class="cookie-label">
                        <img src="icons/jira.png" alt="Jira" class="service-icon">
                        Jira Cookie:
                    </span>
                    <button class="copy-icon" id="copyJira" title="Copy to clipboard">📋</button>
                </div>
                <div class="cookie-value" id="jiraValue"></div>
            </div>
        </div>
        <div id="extractStatus" class="status" style="display: none;"></div>
    </div>
    
    <div class="actions">
        <button id="downloadBat" class="save-btn" style="display: none;">📜 Download .bat File</button>
    </div>
    
    <div class="footer">
        <p>Smals.be Atlassian Integration</p>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
