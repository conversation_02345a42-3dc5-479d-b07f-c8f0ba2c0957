#!/usr/bin/env python3
"""
Example of using Confluence with browser-extracted cookie
This demonstrates how to use browser cookies without hardcoding them
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'mcp-server'))

from extended_atlassian_client import ExtendedAtlassianClient
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

def test_confluence_with_browser_cookie(cookie_value=None):
    """Test Confluence using browser-extracted cookie"""
    print("🍪 Testing Confluence with Browser Cookie")
    print("="*50)
    
    if cookie_value:
        # Set the cookie as environment variable (non-hardcoded approach)
        os.environ['CONFLUENCE_BROWSER_COOKIE'] = cookie_value
        print(f"✅ Using provided browser cookie: {cookie_value[:20]}...")
    else:
        # Check if cookie is already set in environment
        if 'CONFLUENCE_BROWSER_COOKIE' in os.environ:
            print("✅ Using browser cookie from environment variable")
        else:
            print("❌ No browser cookie provided")
            print("Usage:")
            print("  python examples/confluence_with_browser_cookie.py <cookie_value>")
            print("  OR set environment variable: CONFLUENCE_BROWSER_COOKIE=<cookie_value>")
            return False
    
    try:
        # Create client (will automatically use browser cookie if available)
        client = ExtendedAtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        
        # Test connection
        print("\n1. Testing connection...")
        results = client.test_connection()
        print(f"   Confluence: {'✅ Connected' if results.get('confluence') else '❌ Failed'}")
        
        # Test Confluence operations
        if results.get('confluence'):
            print("\n2. Testing Confluence operations...")
            
            # Get spaces
            try:
                spaces = client.confluence.get_spaces(limit=5)
                print(f"   ✅ Spaces: Found {len(spaces.get('results', []))} spaces")
                for space in spaces.get('results', [])[:3]:
                    print(f"      • {space.get('key', 'N/A')}: {space.get('name', 'N/A')}")
            except Exception as e:
                print(f"   ❌ Spaces error: {e}")
            
            # Search content
            try:
                search_result = client.confluence.search_content(
                    cql="type=page", 
                    limit=3
                )
                results_list = search_result.get('results', [])
                print(f"   ✅ Search: Found {len(results_list)} pages")
                for page in results_list:
                    title = page.get('title', 'No title')
                    print(f"      • {title[:50]}...")
            except Exception as e:
                print(f"   ❌ Search error: {e}")
            
            # Get current user
            try:
                user = client.confluence.get_current_user()
                print(f"   ✅ Current user: {user.get('displayName', 'Unknown')}")
            except Exception as e:
                print(f"   ❌ User info error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Client error: {e}")
        return False
    finally:
        # Clean up environment variable if we set it
        if cookie_value and 'CONFLUENCE_BROWSER_COOKIE' in os.environ:
            del os.environ['CONFLUENCE_BROWSER_COOKIE']

def show_cookie_extraction_guide():
    """Show how to extract browser cookies"""
    print("\n📋 How to Extract Browser Cookie")
    print("="*50)
    print("1. Open browser and log into Confluence:")
    print(f"   {ATLASSIAN_CONFIG['CONFLUENCE_URL']}")
    print()
    print("2. Complete 2FA authentication")
    print()
    print("3. Open Developer Tools (F12)")
    print()
    print("4. Go to Application tab > Cookies")
    print()
    print("5. Find 'CONFLUENCESESSIONID' cookie")
    print()
    print("6. Copy the cookie value")
    print()
    print("7. Use it with this script:")
    print("   python examples/confluence_with_browser_cookie.py <cookie_value>")
    print()
    print("8. Or set as environment variable:")
    print("   set CONFLUENCE_BROWSER_COOKIE=<cookie_value>")
    print("   python examples/confluence_with_browser_cookie.py")
    print()

def show_usage_examples():
    """Show practical usage examples"""
    print("💡 Usage Examples")
    print("="*50)
    print()
    print("# Method 1: Pass cookie as argument")
    print("python examples/confluence_with_browser_cookie.py 826908A1CFAD61EB7F4C1FC7B5BDE952")
    print()
    print("# Method 2: Set environment variable")
    print("set CONFLUENCE_BROWSER_COOKIE=826908A1CFAD61EB7F4C1FC7B5BDE952")
    print("python examples/confluence_with_browser_cookie.py")
    print()
    print("# Method 3: In your code")
    print("import os")
    print("os.environ['CONFLUENCE_BROWSER_COOKIE'] = 'your_cookie_here'")
    print("client = ExtendedAtlassianClient(config, **settings)")
    print("# Client will automatically use the browser cookie")
    print()

if __name__ == "__main__":
    print("🍪 Confluence Browser Cookie Integration")
    print("="*60)
    
    # Check if cookie value provided as argument
    cookie_value = sys.argv[1] if len(sys.argv) > 1 else None
    
    if cookie_value or 'CONFLUENCE_BROWSER_COOKIE' in os.environ:
        success = test_confluence_with_browser_cookie(cookie_value)
        
        if success:
            print("\n🎉 SUCCESS! Confluence integration working with browser cookie!")
            print("\n💡 To use this in production:")
            print("1. Extract fresh cookie from browser when needed")
            print("2. Set CONFLUENCE_BROWSER_COOKIE environment variable")
            print("3. Use normal client code - cookie will be used automatically")
        else:
            print("\n❌ Browser cookie approach failed")
    else:
        show_cookie_extraction_guide()
        show_usage_examples()
        
        print("\n🔍 Want to test with the current cookie?")
        print("Run: python examples/confluence_with_browser_cookie.py 826908A1CFAD61EB7F4C1FC7B5BDE952")
