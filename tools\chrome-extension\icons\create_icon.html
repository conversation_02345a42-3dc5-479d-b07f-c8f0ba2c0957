<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icon</title>
</head>
<body>
    <canvas id="canvas" width="48" height="48"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Create a simple cookie icon
        ctx.fillStyle = '#4285f4';
        ctx.fillRect(0, 0, 48, 48);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '32px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('🍪', 24, 35);
        
        // Download the icon
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'icon48.png';
            a.click();
        });
    </script>
</body>
</html>
