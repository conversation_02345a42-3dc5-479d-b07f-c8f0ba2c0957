#!/usr/bin/env python3
"""
Simple authentication test for Atlassian APIs

This script tests the basic authentication to help diagnose connection issues.
"""

import requests
import base64
import json
import sys
import os

# Add parent directory to path to import config
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from config import ATLASSIAN_CONFIG

# Use configuration from config.py
CONFIG = ATLASSIAN_CONFIG


def test_basic_auth(service_name, base_url, username, api_token, endpoint):
    """Test basic authentication to an Atlassian service"""
    print(f"\n=== Testing {service_name} Authentication ===")
    
    # Create basic auth header
    credentials = f"{username}:{api_token}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    auth_header = f"Basic {encoded_credentials}"
    
    # Build full URL
    full_url = f"{base_url.rstrip('/')}{endpoint}"
    
    print(f"URL: {full_url}")
    print(f"Username: {username}")
    print(f"API Token: {api_token[:10]}...{api_token[-5:]}")  # Masked for security
    
    headers = {
        'Authorization': auth_header,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    try:
        print("Making request...")
        response = requests.get(full_url, headers=headers, timeout=30, verify=True)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("SUCCESS: Authentication successful!")
            try:
                data = response.json()
                print(f"Response data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            except:
                print("Response is not JSON")
            return True
        elif response.status_code == 401:
            print("FAIL: Authentication failed (401 Unauthorized)")
            print("This could mean:")
            print("- Invalid username or API token")
            print("- API token has expired")
            print("- Account is locked or disabled")
            print("- Two-factor authentication is required")
        elif response.status_code == 403:
            print("FAIL: Access forbidden (403 Forbidden)")
            print("Authentication succeeded but user lacks permissions")
        elif response.status_code == 404:
            print("FAIL: Endpoint not found (404 Not Found)")
            print("The API endpoint might not exist or be available")
        else:
            print(f"FAIL: Unexpected status code {response.status_code}")
        
        print(f"Response text: {response.text[:500]}...")
        return False
        
    except requests.exceptions.SSLError as e:
        print(f"SSL Error: {e}")
        print("Try setting verify=False if using self-signed certificates")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"Connection Error: {e}")
        print("Check if the server is reachable and the URL is correct")
        return False
    except requests.exceptions.Timeout as e:
        print(f"Timeout Error: {e}")
        print("The server took too long to respond")
        return False
    except Exception as e:
        print(f"Unexpected Error: {e}")
        return False


def test_network_connectivity():
    """Test basic network connectivity to the servers"""
    print("\n=== Testing Network Connectivity ===")
    
    urls_to_test = [
        CONFIG["CONFLUENCE_URL"],
        CONFIG["JIRA_URL"]
    ]
    
    for url in urls_to_test:
        print(f"\nTesting connectivity to: {url}")
        try:
            response = requests.get(url, timeout=10, verify=True)
            print(f"Status: {response.status_code}")
            print(f"Server: {response.headers.get('Server', 'Unknown')}")
            print("SUCCESS: Server is reachable")
        except Exception as e:
            print(f"FAIL: {e}")


def test_api_endpoints():
    """Test different API endpoints to find working ones"""
    print("\n=== Testing Different API Endpoints ===")
    
    # Confluence endpoints to try
    confluence_endpoints = [
        "/rest/api/space",
        "/rest/api/content",
        "/rest/api/user/current",
        "/rest/api/settings/lookandfeel"
    ]
    
    # Jira endpoints to try
    jira_endpoints = [
        "/rest/api/2/serverInfo",
        "/rest/api/2/myself",
        "/rest/api/2/project",
        "/rest/api/2/issue/createmeta"
    ]
    
    print("\nTesting Confluence endpoints:")
    for endpoint in confluence_endpoints:
        print(f"\nTesting: {endpoint}")
        test_basic_auth(
            "Confluence",
            CONFIG["CONFLUENCE_URL"],
            CONFIG["CONFLUENCE_USERNAME"],
            CONFIG["CONFLUENCE_API_TOKEN"],
            endpoint
        )
    
    print("\nTesting Jira endpoints:")
    for endpoint in jira_endpoints:
        print(f"\nTesting: {endpoint}")
        test_basic_auth(
            "Jira",
            CONFIG["JIRA_URL"],
            CONFIG["JIRA_USERNAME"],
            CONFIG["JIRA_API_TOKEN"],
            endpoint
        )


def main():
    """Main diagnostic function"""
    print("Atlassian Authentication Diagnostic Tool")
    print("=" * 50)
    
    # Test network connectivity first
    test_network_connectivity()
    
    # Test basic authentication
    confluence_success = test_basic_auth(
        "Confluence",
        CONFIG["CONFLUENCE_URL"],
        CONFIG["CONFLUENCE_USERNAME"],
        CONFIG["CONFLUENCE_API_TOKEN"],
        "/rest/api/space"
    )
    
    jira_success = test_basic_auth(
        "Jira",
        CONFIG["JIRA_URL"],
        CONFIG["JIRA_USERNAME"],
        CONFIG["JIRA_API_TOKEN"],
        "/rest/api/2/serverInfo"
    )
    
    # If basic tests fail, try other endpoints
    if not confluence_success or not jira_success:
        test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 50)
    print(f"Confluence: {'SUCCESS' if confluence_success else 'FAILED'}")
    print(f"Jira: {'SUCCESS' if jira_success else 'FAILED'}")
    
    if not confluence_success or not jira_success:
        print("\nTROUBLESHOOTING STEPS:")
        print("1. Verify your username and API tokens are correct")
        print("2. Check if your account is active and not locked")
        print("3. Ensure you have permission to access the REST API")
        print("4. Contact your Atlassian administrator if issues persist")
        print("5. Check if the servers require VPN or special network access")
    
    return 0 if confluence_success and jira_success else 1


if __name__ == "__main__":
    sys.exit(main())
