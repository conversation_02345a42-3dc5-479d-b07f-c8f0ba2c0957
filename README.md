# Atlassian Companion

A comprehensive Python client for interacting with Atlassian on-premise installations (<PERSON>flue<PERSON> and Jira) via their REST APIs, with MCP (Model Context Protocol) server support.

## 🎯 Current Status

✅ **Working Features:**
- **Jira Integration**: Full access to projects, issues, search functionality
- **Session Authentication**: Handles 2FA scenarios properly
- **MCP Server**: 7/8 tests passing, ready for production use
- **Connection Testing**: Verify connectivity to both services

⚠️ **Known Limitations:**
- **Confluence REST API**: Blocked by server-side 2FA policies (login works, but API access restricted)

## 🚀 Quick Start

### 1. Test Connection
```bash
python main.py test
```

### 2. Use Jira Tools
```bash
python examples/jira_usage.py
```

### 3. Start MCP Server
```bash
python mcp-server/server.py
```

## 📁 Project Structure

```
atlassian-companion/
├── client/                     # Core Atlassian client library
│   ├── __init__.py            # Package initialization
│   └── atlassian_client.py    # Main client implementation
├── mcp-server/                # MCP (Model Context Protocol) server
│   ├── extended_atlassian_client.py  # Extended client for MCP
│   ├── server.py              # MCP server implementation
│   ├── test_mcp_server.py     # MCP server tests
│   └── mcp_config_env.json    # Environment-based MCP config
├── examples/                  # Usage examples
│   ├── jira_usage.py          # Jira-specific examples
│   └── test_connection.py     # Connection testing
├── tests/                     # Test suite
├── tools/                     # Utility tools
│   └── debug_auth.py          # Authentication debugging
├── docs/                      # Documentation
├── config.py                  # Configuration with your credentials
└── main.py                    # Main entry point
```

## ✨ Features

- **Confluence API Support**: Create, read, update, and delete pages; manage spaces; search content using CQL
- **Jira API Support**: Manage projects, issues, comments; search using JQL; handle issue types and workflows
- **Personal Access Token Authentication**: Support for Bearer token authentication
- **Error Handling**: Comprehensive error handling with custom exceptions
- **Pagination**: Built-in pagination support for large datasets
- **Utilities**: Helper functions for common operations and query building
- **Command-Line Interface**: Easy-to-use CLI for common operations

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Test Connection

```bash
python main.py test
```

### 3. List Confluence Spaces

```bash
python main.py spaces
```

### 4. List Jira Projects

```bash
python main.py projects
```

### 5. Search Content

```bash
python main.py search-confluence --query "API documentation"
python main.py search-jira --query "bug fix"
```

## ⚙️ Configuration

The configuration is managed in `config.py`. Update this file with your Atlassian credentials:

```python
# config.py
ATLASSIAN_CONFIG = {
    "CONFLUENCE_URL": "https://your-confluence-server.com",
    "CONFLUENCE_USERNAME": "<EMAIL>",
    "CONFLUENCE_API_TOKEN": "your_personal_access_token",
    "JIRA_URL": "https://your-jira-server.com",
    "JIRA_USERNAME": "<EMAIL>",
    "JIRA_API_TOKEN": "your_personal_access_token"
}

AUTH_METHOD = 'bearer'  # Use 'bearer' for Personal Access Tokens
```

### Getting Personal Access Tokens

For Atlassian on-premise installations:
1. Log into your Confluence/Jira instance
2. Go to your profile settings
3. Look for "Personal Access Tokens" or "API Tokens"
4. Generate new tokens with appropriate permissions
5. Update `config.py` with your tokens

Contact your Atlassian administrator if you need help accessing these settings.

## 📚 Programming Interface

```python
from client import AtlassianClient, ConfluenceAPI, JiraAPI
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

# Initialize the client
client = AtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)

# Test connectivity
connection_status = client.test_connection()
print(f"Confluence: {connection_status['confluence']}")
print(f"Jira: {connection_status['jira']}")

# Use Confluence API
confluence = ConfluenceAPI(client)
spaces = confluence.get_spaces(limit=10)
print(f"Found {len(spaces['results'])} spaces")

# Use Jira API
jira = JiraAPI(client)
projects = jira.get_projects()
print(f"Found {len(projects)} projects")
```

## Confluence API Examples

### Working with Spaces

```python
confluence = ConfluenceAPI(client)

# Get all spaces
spaces = confluence.get_spaces(limit=50, expand='description')

# Get specific space
space = confluence.get_space('MYSPACE', expand='description,homepage')
```

### Working with Pages

```python
# Get pages from a space
pages = confluence.get_pages(space_key='MYSPACE', limit=25, expand='body.storage,version')

# Get specific page
page = confluence.get_page('123456', expand='body.storage,version,ancestors')

# Create a new page
new_page = confluence.create_page(
    space_key='MYSPACE',
    title='My New Page',
    content='<p>This is the page content in HTML format.</p>',
    parent_id='123456'  # Optional parent page
)

# Update existing page
updated_page = confluence.update_page(
    page_id='123456',
    title='Updated Page Title',
    content='<p>Updated content.</p>',
    version=2  # Current version number
)

# Delete page
confluence.delete_page('123456')
```

### Searching Content

```python
from atlassian_client import AtlassianUtils

# Build CQL query
cql = AtlassianUtils.build_cql_query(
    space='MYSPACE',
    title='Getting Started',
    content_type='page'
)

# Search using CQL
results = confluence.search_content(cql, limit=50, expand='body.excerpt')
```

## Jira API Examples

### Working with Projects

```python
jira = JiraAPI(client)

# Get all projects
projects = jira.get_projects(expand='description,lead')

# Get specific project
project = jira.get_project('MYPROJ', expand='issueTypes,versions')
```

### Working with Issues

```python
# Search for issues using JQL
from atlassian_client import AtlassianUtils

jql = AtlassianUtils.build_jql_query(
    project='MYPROJ',
    status='Open',
    assignee='john.doe'
)

issues = jira.search_issues(jql, max_results=50, fields=['summary', 'status', 'assignee'])

# Get specific issue
issue = jira.get_issue('MYPROJ-123', expand='changelog')

# Create new issue
new_issue = jira.create_issue(
    project_key='MYPROJ',
    summary='Bug in login system',
    issue_type='Bug',
    description='Users cannot log in with special characters in password',
    assignee='john.doe',
    priority='High',
    labels=['login', 'security']
)

# Update issue
jira.update_issue(
    issue_key='MYPROJ-123',
    fields={
        'summary': 'Updated summary',
        'priority': {'name': 'Medium'}
    }
)

# Add comment
comment = jira.add_comment(
    issue_key='MYPROJ-123',
    comment='This issue has been investigated and a fix is in progress.'
)
```

## Pagination Support

For large datasets, use the pagination helper:

```python
from atlassian_client import PaginationHelper

# Get all pages across all spaces
all_pages = list(PaginationHelper.get_all_pages(
    confluence.get_pages,
    limit=50  # Page size
))

# Get all issues from a project
all_issues = list(PaginationHelper.get_all_pages(
    jira.search_issues,
    'project = "MYPROJ"',
    max_results=100
))
```

## Utility Functions

### Query Building

```python
from atlassian_client import AtlassianUtils

# Build CQL for Confluence
cql = AtlassianUtils.build_cql_query(
    space='DOCS',
    title='API',
    content_type='page',
    creator='john.doe'
)

# Build JQL for Jira
jql = AtlassianUtils.build_jql_query(
    project='PROJ',
    issue_type='Bug',
    status='Open',
    created_date='-1w'
)
```

### Content Formatting

```python
# Convert markdown to Confluence HTML
markdown_content = """
# Title
This is **bold** and *italic* text.
"""

html_content = AtlassianUtils.format_confluence_content(markdown_content, 'markdown')
```

### URL Parsing

```python
# Extract IDs from URLs
page_id = AtlassianUtils.extract_page_id_from_url(
    'https://your-confluence-server.com/pages/viewpage.action?pageId=123456'
)

issue_key = AtlassianUtils.extract_issue_key_from_url(
    'https://your-jira-server.com/browse/PROJ-123'
)
```

## Error Handling

The client provides specific exceptions for different error types:

```python
from atlassian_client import AtlassianError, AuthenticationError, APIError

try:
    page = confluence.get_page('invalid-id')
except AuthenticationError:
    print("Authentication failed - check your credentials")
except APIError as e:
    print(f"API error: {e} (Status: {e.status_code})")
except AtlassianError as e:
    print(f"General Atlassian error: {e}")
```

## Logging

The client uses Python's logging module. Configure logging to see detailed information:

```python
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('atlassian_client')
```

## 🧪 Testing

### Run All Tests

```bash
cd tests
python test_client.py
```

### Test Authentication Only

```bash
cd tests
python test_auth.py
```

### Test Personal Access Tokens

```bash
cd tests
python test_pat.py
```

### Setup and Test (Automated)

```bash
cd tests
python setup_and_test.py
```

## 📖 Examples

### Run Comprehensive Examples

```bash
cd examples
python example_usage.py
```

### Command-Line Interface Examples

```bash
# Test connection
python main.py test

# List Confluence spaces
python main.py spaces

# List Jira projects
python main.py projects

# Search Confluence
python main.py search-confluence --query "documentation"

# Search Jira
python main.py search-jira --query "bug"
```

## API Reference

### AtlassianClient

Main client class for authentication and connection management.

**Methods:**
- `test_connection()`: Test connectivity to both services
- `_make_request()`: Internal method for making authenticated requests

### ConfluenceAPI

Confluence-specific operations.

**Methods:**
- `get_spaces()`, `get_space()`: Space management
- `get_pages()`, `get_page()`, `create_page()`, `update_page()`, `delete_page()`: Page operations
- `search_content()`: Content search using CQL
- `get_page_children()`: Get child pages

### JiraAPI

Jira-specific operations.

**Methods:**
- `get_server_info()`: Server information
- `get_projects()`, `get_project()`: Project management
- `search_issues()`, `get_issue()`, `create_issue()`, `update_issue()`: Issue operations
- `add_comment()`: Comment management
- `get_issue_types()`: Issue type information

### Utility Classes

- `PaginationHelper`: Handle paginated responses
- `AtlassianUtils`: Common utility functions

## License

This code is provided as-is for use with Atlassian on-premise installations.

## Support

For issues or questions, contact your system administrator or the development team.
