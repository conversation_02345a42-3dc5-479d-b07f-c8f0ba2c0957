#!/usr/bin/env python3
"""
Test script for the Atlassian MCP Server

This script tests the MCP server functionality without requiring a full MCP client.
"""

import asyncio
import json
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from mcp.types import (
    ReadResourceRequest, ListResourcesRequest, ListToolsRequest
)

# Import the server handlers directly
from server import (
    list_resources, read_resource, list_tools, handle_call_tool,
    get_client
)


async def test_connection():
    """Test basic connectivity"""
    print("🔗 Testing Atlassian connectivity...")
    
    try:
        client = get_client()
        result = client.test_connection()
        
        print(f"Confluence: {'✅ Connected' if result['confluence'] else '❌ Failed'}")
        print(f"Jira: {'✅ Connected' if result['jira'] else '❌ Failed'}")
        
        return result['confluence'] or result['jira']
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False


async def test_list_resources():
    """Test resource listing"""
    print("\n📚 Testing resource listing...")
    
    try:
        resources = await list_resources()
        print(f"Found {len(resources)} resources:")
        
        for resource in resources[:10]:  # Show first 10
            print(f"  - {resource.name}: {resource.uri}")
        
        if len(resources) > 10:
            print(f"  ... and {len(resources) - 10} more")
        
        return True
    except Exception as e:
        print(f"❌ Resource listing failed: {e}")
        return False


async def test_get_resource():
    """Test getting a specific resource"""
    print("\n📄 Testing resource retrieval...")
    
    try:
        # Test system info resource
        request = ReadResourceRequest(
            method="resources/read",
            params={"uri": "atlassian://system/info"}
        )
        result = await read_resource(request)
        
        print("✅ System info resource retrieved successfully")
        content = result.contents[0].text
        data = json.loads(content)
        
        print(f"System info keys: {list(data.keys())}")
        return True
    except Exception as e:
        print(f"❌ Resource retrieval failed: {e}")
        return False


async def test_list_tools():
    """Test tool listing"""
    print("\n🔧 Testing tool listing...")
    
    try:
        tools = await list_tools()
        print(f"Found {len(tools)} tools:")
        
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        
        return True
    except Exception as e:
        print(f"❌ Tool listing failed: {e}")
        return False


async def test_system_tools():
    """Test system tools"""
    print("\n⚙️ Testing system tools...")
    
    try:
        # Test connection tool
        result = await handle_call_tool("atlassian_test_connection", {})

        print("✅ Connection tool executed successfully")

        # Test system info tool
        result = await handle_call_tool("atlassian_get_system_info", {})
        
        print("✅ System info tool executed successfully")
        return True
    except Exception as e:
        print(f"❌ System tools test failed: {e}")
        return False


async def test_confluence_tools():
    """Test Confluence tools"""
    print("\n📝 Testing Confluence tools...")
    
    try:
        client = get_client()
        connection_status = client.test_connection()
        
        if not connection_status.get('confluence', False):
            print("⚠️ Skipping Confluence tools (no connection)")
            return True
        
        # Test get spaces
        result = await handle_call_tool("confluence_get_spaces", {"limit": 5})
        print("✅ Get spaces tool executed successfully")

        # Test search (simple query)
        result = await handle_call_tool("confluence_search", {
            "cql": "type = page",
            "limit": 3
        })
        print("✅ Search tool executed successfully")
        
        return True
    except Exception as e:
        print(f"❌ Confluence tools test failed: {e}")
        return False


async def test_jira_tools():
    """Test Jira tools"""
    print("\n🎫 Testing Jira tools...")
    
    try:
        client = get_client()
        connection_status = client.test_connection()
        
        if not connection_status.get('jira', False):
            print("⚠️ Skipping Jira tools (no connection)")
            return True
        
        # Test get projects
        result = await handle_call_tool("jira_get_projects", {})
        print("✅ Get projects tool executed successfully")

        # Test search issues (simple query)
        result = await handle_call_tool("jira_search_issues", {
            "jql": "ORDER BY updated DESC",
            "max_results": 3
        })
        print("✅ Search issues tool executed successfully")
        
        return True
    except Exception as e:
        print(f"❌ Jira tools test failed: {e}")
        return False


async def test_error_handling():
    """Test error handling"""
    print("\n🚨 Testing error handling...")
    
    try:
        # Test invalid tool
        result = await handle_call_tool("invalid_tool", {})
        
        if result.isError:
            print("✅ Error handling works correctly")
            return True
        else:
            print("❌ Error handling failed - should have returned error")
            return False
    except Exception as e:
        print(f"✅ Error handling works correctly (exception caught): {e}")
        return True


async def run_comprehensive_test():
    """Run all tests"""
    print("🧪 Atlassian MCP Server Test Suite")
    print("=" * 50)
    
    tests = [
        ("Connection Test", test_connection),
        ("List Resources", test_list_resources),
        ("Get Resource", test_get_resource),
        ("List Tools", test_list_tools),
        ("System Tools", test_system_tools),
        ("Confluence Tools", test_confluence_tools),
        ("Jira Tools", test_jira_tools),
        ("Error Handling", test_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! MCP server is ready to use.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1


async def main():
    """Main test function"""
    try:
        exit_code = await run_comprehensive_test()
        return exit_code
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error during testing: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
