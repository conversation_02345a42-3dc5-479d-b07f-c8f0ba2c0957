#!/usr/bin/env python3
"""
Helper tool to test Confluence REST API with browser-extracted cookies
Based on Stack Overflow solution - using actual browser session cookies
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import requests
from config import ATLASSIAN_CONFIG

def test_with_browser_cookie(jsession_cookie_value):
    """Test Confluence REST API with browser-extracted JSESSION cookie"""
    print("🌐 Testing with Browser-Extracted Cookie")
    print("="*50)
    
    # Create cookies dict
    cookies = {
        'CONFLUENCESESSIONID': jsession_cookie_value
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Test different endpoints
    api_endpoints = [
        "/rest/api/space?limit=1",
        "/rest/api/user/current",
        "/rest/api/content?limit=1"
    ]
    
    for endpoint in api_endpoints:
        url = f"{ATLASSIAN_CONFIG['CONFLUENCE_URL']}{endpoint}"
        print(f"\nTesting: {endpoint}")
        
        try:
            response = requests.get(url, headers=headers, cookies=cookies, verify=False, timeout=10)
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  ✅ SUCCESS!")
                data = response.json()
                if 'results' in data:
                    print(f"  Found {len(data['results'])} results")
                    if data['results']:
                        first_item = data['results'][0]
                        if 'name' in first_item:
                            print(f"  First item: {first_item['name']}")
                        elif 'key' in first_item:
                            print(f"  First item: {first_item['key']}")
                elif 'displayName' in data:
                    print(f"  User: {data['displayName']}")
                else:
                    print(f"  Response keys: {list(data.keys())}")
                return True
            else:
                print(f"  ❌ Failed: {response.text[:100]}")
                
        except Exception as e:
            print(f"  Error: {e}")
    
    return False

def show_cookie_extraction_instructions():
    """Show instructions for extracting cookies from browser"""
    print("📋 How to Extract JSESSION Cookie from Browser")
    print("="*60)
    print()
    print("1. Open your browser and go to:")
    print(f"   {ATLASSIAN_CONFIG['CONFLUENCE_URL']}")
    print()
    print("2. Log in with your credentials + Microsoft Authenticator")
    print()
    print("3. Once logged in, open Developer Tools (F12)")
    print()
    print("4. Go to the 'Application' tab (Chrome) or 'Storage' tab (Firefox)")
    print()
    print("5. In the left sidebar, expand 'Cookies'")
    print()
    print("6. Click on your Confluence domain")
    print()
    print("7. Look for a cookie named 'CONFLUENCESESSIONID' or similar")
    print()
    print("8. Copy the cookie value (long string of characters)")
    print()
    print("9. Run this script again with the cookie value:")
    print("   python tools/browser_cookie_helper.py <cookie_value>")
    print()
    print("Example cookie value:")
    print("   F07E8CCB9051D0886D0EA8631229758F")
    print()

def test_alternative_approaches():
    """Test alternative approaches that might work"""
    print("🔧 Testing Alternative Approaches")
    print("="*50)
    
    # Test if any endpoints work without authentication
    print("\n1. Testing public endpoints...")
    public_endpoints = [
        "/status",
        "/rest/api/space?limit=1&status=current",
        "/rest/api/content?limit=1&status=current"
    ]
    
    for endpoint in public_endpoints:
        url = f"{ATLASSIAN_CONFIG['CONFLUENCE_URL']}{endpoint}"
        try:
            response = requests.get(url, verify=False, timeout=10)
            print(f"  {endpoint}: {response.status_code}")
            if response.status_code == 200:
                print(f"    ✅ Public access available!")
        except Exception as e:
            print(f"  {endpoint}: Error - {e}")
    
    # Test if basic auth works for any endpoints
    print("\n2. Testing basic auth on different endpoints...")
    username = ATLASSIAN_CONFIG['CONFLUENCE_USERNAME']
    token = ATLASSIAN_CONFIG['CONFLUENCE_API_TOKEN']
    
    import base64
    credentials = f"{username}:{token}"
    encoded = base64.b64encode(credentials.encode()).decode()
    
    headers = {
        'Authorization': f"Basic {encoded}",
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    basic_auth_endpoints = [
        "/rest/api/user/current",
        "/rest/api/space?limit=1",
        "/rest/api/content?limit=1&expand=space"
    ]
    
    for endpoint in basic_auth_endpoints:
        url = f"{ATLASSIAN_CONFIG['CONFLUENCE_URL']}{endpoint}"
        try:
            response = requests.get(url, headers=headers, verify=False, timeout=10)
            print(f"  {endpoint}: {response.status_code}")
            if response.status_code == 200:
                print(f"    ✅ Basic auth works!")
        except Exception as e:
            print(f"  {endpoint}: Error - {e}")

if __name__ == "__main__":
    print("🍪 Browser Cookie Helper for Confluence REST API")
    print("="*60)
    
    if len(sys.argv) > 1:
        # Cookie value provided
        cookie_value = sys.argv[1]
        print(f"Testing with provided cookie: {cookie_value[:20]}...")
        
        if test_with_browser_cookie(cookie_value):
            print("\n🎉 SUCCESS! Browser cookie approach works!")
            print("You can now use this method in your code.")
        else:
            print("\n❌ Browser cookie approach also failed.")
            print("The REST API might be completely disabled for 2FA users.")
    else:
        # No cookie provided, show instructions
        show_cookie_extraction_instructions()
        test_alternative_approaches()
        
        print("\n" + "="*60)
        print("💡 Summary:")
        print("1. Try extracting the JSESSION cookie from your browser")
        print("2. If that doesn't work, the REST API might be disabled")
        print("3. Focus on using the working Jira integration")
        print("4. Contact your Confluence admin about REST API access")
