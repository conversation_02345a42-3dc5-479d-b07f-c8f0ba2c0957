# Atlassian MCP Server

A Model Context Protocol (MCP) server that provides comprehensive access to Atlassian on-premise installations (Confluence and Jira) via their REST APIs.

This server exposes Atlassian functionality as MCP tools and resources, allowing AI assistants to interact with Confluence and Jira seamlessly.

## Features

### 🔧 **Comprehensive API Coverage**

**Confluence Operations:**
- ✅ Search content using CQL (Confluence Query Language)
- ✅ Get, create, update, and delete pages
- ✅ Manage spaces and permissions
- ✅ Handle comments, attachments, and labels
- ✅ Page history and version management
- ✅ User and permission management

**Jira Operations:**
- ✅ Search issues using JQL (Jira Query Language)
- ✅ Get, create, update, and transition issues
- ✅ Manage projects, components, and versions
- ✅ Handle comments, attachments, and watchers
- ✅ Issue linking and workflow management
- ✅ User permissions and role management

### 🌐 **MCP Integration**

**Resources:**
- Dynamic resource discovery based on available spaces and projects
- Real-time system information and connectivity status
- Structured access to Confluence spaces and Jira projects
- User-specific dashboards and filters

**Tools:**
- 20+ tools covering all major Atlassian operations
- Comprehensive input validation and error handling
- Detailed response formatting for AI consumption
- Support for both read and write operations

## Installation

### Prerequisites

- Python 3.8 or higher
- Access to Atlassian on-premise installations
- Valid Personal Access Tokens for authentication

### Setup

1. **Install dependencies:**
   ```bash
   cd mcp-server
   pip install -r requirements.txt
   ```

2. **Configure credentials:**
   Update the parent `config.py` file with your Atlassian credentials:
   ```python
   ATLASSIAN_CONFIG = {
       "CONFLUENCE_URL": "https://your-confluence-server.com",
       "CONFLUENCE_USERNAME": "<EMAIL>",
       "CONFLUENCE_API_TOKEN": "your_confluence_personal_access_token",
       "JIRA_URL": "https://your-jira-server.com",
       "JIRA_USERNAME": "<EMAIL>",
       "JIRA_API_TOKEN": "your_jira_personal_access_token"
   }
   ```

3. **Test the server:**
   ```bash
   python server.py
   ```

## Usage

### MCP Client Configuration

Add this server to your MCP client configuration:

```json
{
  "mcpServers": {
    "atlassian": {
      "command": "python",
      "args": ["/path/to/mcp-server/server.py"],
      "env": {
        "PYTHONPATH": "/path/to/atlassian-companion"
      }
    }
  }
}
```

### Available Resources

The server dynamically exposes resources based on your Atlassian access:

- `atlassian://system/info` - System information and connectivity
- `atlassian://confluence/spaces` - List of Confluence spaces
- `atlassian://confluence/spaces/{key}` - Specific space details
- `atlassian://confluence/spaces/{key}/pages` - Pages in a space
- `atlassian://jira/projects` - List of Jira projects
- `atlassian://jira/projects/{key}` - Specific project details
- `atlassian://jira/projects/{key}/issues` - Issues in a project
- `atlassian://jira/user` - Current user information
- `atlassian://jira/filters` - User's saved filters
- `atlassian://jira/dashboards` - User's dashboards

### Available Tools

**System Tools:**
- `atlassian_test_connection` - Test connectivity to services
- `atlassian_get_system_info` - Get system information

**Confluence Tools:**
- `confluence_search` - Search content using CQL
- `confluence_get_page` - Get page by ID
- `confluence_create_page` - Create new page
- `confluence_update_page` - Update existing page
- `confluence_get_spaces` - List spaces
- `confluence_get_page_comments` - Get page comments
- `confluence_add_comment` - Add comment to page

**Jira Tools:**
- `jira_search_issues` - Search issues using JQL
- `jira_get_issue` - Get issue by key
- `jira_create_issue` - Create new issue
- `jira_update_issue` - Update existing issue
- `jira_add_comment` - Add comment to issue
- `jira_transition_issue` - Transition issue status
- `jira_get_projects` - List projects

## Examples

### Search Confluence Content

```python
# Using the confluence_search tool
{
  "name": "confluence_search",
  "arguments": {
    "cql": "space = \"027INFOSEC\" AND type = \"page\" AND text ~ \"security\"",
    "limit": 10,
    "expand": "body.excerpt"
  }
}
```

### Create a Jira Issue

```python
# Using the jira_create_issue tool
{
  "name": "jira_create_issue",
  "arguments": {
    "project_key": "INFOSEC",
    "summary": "Security vulnerability in API",
    "issue_type": "Bug",
    "description": "Found a potential security issue in the authentication flow",
    "priority": "High",
    "labels": ["security", "api"]
  }
}
```

### Update a Confluence Page

```python
# Using the confluence_update_page tool
{
  "name": "confluence_update_page",
  "arguments": {
    "page_id": "123456",
    "title": "Updated API Documentation",
    "content": "<p>This page contains the latest API documentation.</p>",
    "version": 5
  }
}
```

## Architecture

### Extended Client

The MCP server uses an extended version of the base Atlassian client with additional functionality:

- **ExtendedConfluenceAPI**: Enhanced Confluence operations
- **ExtendedJiraAPI**: Enhanced Jira operations  
- **ExtendedAtlassianClient**: Unified client with both APIs

### MCP Integration

- **Resources**: Dynamic discovery and structured access to Atlassian content
- **Tools**: Comprehensive tool set for all major operations
- **Error Handling**: Robust error handling with detailed error messages
- **Authentication**: Secure Personal Access Token authentication

## Security

- Uses Personal Access Tokens for authentication (more secure than passwords)
- All credentials are managed through configuration files
- SSL verification enabled by default
- Comprehensive error handling prevents credential leakage

## Development

### Running Tests

```bash
# Test the extended client
python -m pytest tests/

# Test MCP server functionality
python test_mcp_server.py
```

### Code Quality

```bash
# Format code
black .
isort .

# Type checking
mypy .
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify Personal Access Tokens are valid
2. **Connection Issues**: Check network connectivity to Atlassian servers
3. **Permission Issues**: Ensure account has API access permissions
4. **MCP Client Issues**: Verify MCP client configuration and Python path

### Debug Mode

Enable debug logging by setting the log level:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the parent project documentation
3. Contact your Atlassian administrator for permission issues
