"""
Extended Atlassian Client for MCP Server

This module extends the base Atlassian client with additional functionality
specifically designed for MCP server operations, including more comprehensive
API coverage and MCP-friendly data structures.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from client.atlassian_client import *
from typing import Dict, List, Optional, Any, Union
import json
from datetime import datetime, timedelta


class ExtendedConfluenceAPI(ConfluenceAPI):
    """Extended Confluence API with additional functionality for MCP"""
    
    def get_page_attachments(self, page_id: str, limit: int = 50, start: int = 0) -> Dict[str, Any]:
        """Get attachments for a specific page"""
        params = {'limit': limit, 'start': start}
        url = self.client._get_confluence_url(f'content/{page_id}/child/attachment')
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json()
    
    def download_attachment(self, attachment_id: str) -> bytes:
        """Download attachment content"""
        url = self.client._get_confluence_url(f'content/{attachment_id}/download')
        response = self.client._make_request('GET', url, self.client.confluence_auth)
        return response.content
    
    def upload_attachment(self, page_id: str, file_path: str, comment: str = None) -> Dict[str, Any]:
        """Upload an attachment to a page"""
        import os
        from requests_toolbelt.multipart.encoder import MultipartEncoder
        
        filename = os.path.basename(file_path)
        
        with open(file_path, 'rb') as f:
            multipart_data = MultipartEncoder(
                fields={
                    'file': (filename, f, 'application/octet-stream'),
                    'comment': comment or f'Uploaded {filename}'
                }
            )
            
            headers = {
                'Authorization': self.client.confluence_auth,
                'Content-Type': multipart_data.content_type,
                'X-Atlassian-Token': 'no-check'
            }
            
            url = self.client._get_confluence_url(f'content/{page_id}/child/attachment')
            response = self.client.session.put(url, data=multipart_data, headers=headers)
            
            if response.status_code >= 400:
                raise APIError(f"Upload failed: {response.text}", response.status_code, response)
            
            return response.json()
    
    def get_page_history(self, page_id: str, limit: int = 25, start: int = 0) -> Dict[str, Any]:
        """Get page version history"""
        params = {'limit': limit, 'start': start, 'expand': 'version'}
        url = self.client._get_confluence_url(f'content/{page_id}/history')
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json()
    
    def get_page_labels(self, page_id: str) -> List[Dict[str, Any]]:
        """Get labels for a page"""
        url = self.client._get_confluence_url(f'content/{page_id}/label')
        response = self.client._make_request('GET', url, self.client.confluence_auth)
        return response.json().get('results', [])
    
    def add_page_label(self, page_id: str, label: str) -> Dict[str, Any]:
        """Add a label to a page"""
        data = [{'prefix': 'global', 'name': label}]
        url = self.client._get_confluence_url(f'content/{page_id}/label')
        response = self.client._make_request('POST', url, self.client.confluence_auth, json=data)
        return response.json()
    
    def remove_page_label(self, page_id: str, label: str) -> bool:
        """Remove a label from a page"""
        url = self.client._get_confluence_url(f'content/{page_id}/label/{label}')
        response = self.client._make_request('DELETE', url, self.client.confluence_auth)
        return response.status_code == 204
    
    def get_page_comments(self, page_id: str, limit: int = 25, start: int = 0) -> Dict[str, Any]:
        """Get comments for a page"""
        params = {'limit': limit, 'start': start, 'expand': 'body.view,version,author'}
        url = self.client._get_confluence_url(f'content/{page_id}/child/comment')
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json()
    
    def add_page_comment(self, page_id: str, comment: str) -> Dict[str, Any]:
        """Add a comment to a page"""
        data = {
            'type': 'comment',
            'container': {'id': page_id, 'type': 'page'},
            'body': {
                'storage': {
                    'value': comment,
                    'representation': 'storage'
                }
            }
        }
        url = self.client._get_confluence_url('content')
        response = self.client._make_request('POST', url, self.client.confluence_auth, json=data)
        return response.json()
    
    def get_space_permissions(self, space_key: str) -> Dict[str, Any]:
        """Get permissions for a space"""
        url = self.client._get_confluence_url(f'space/{space_key}/permission')
        response = self.client._make_request('GET', url, self.client.confluence_auth)
        return response.json()
    
    def get_user_details(self, username: str = None) -> Dict[str, Any]:
        """Get user details (current user if no username provided)"""
        if username:
            params = {'username': username}
            url = self.client._get_confluence_url('user')
        else:
            params = {}
            url = self.client._get_confluence_url('user/current')
        
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json()
    
    def get_content_restrictions(self, content_id: str) -> Dict[str, Any]:
        """Get content restrictions"""
        url = self.client._get_confluence_url(f'content/{content_id}/restriction')
        response = self.client._make_request('GET', url, self.client.confluence_auth)
        return response.json()
    
    def create_space(self, key: str, name: str, description: str = None) -> Dict[str, Any]:
        """Create a new space"""
        data = {
            'key': key,
            'name': name,
            'type': 'global'
        }
        if description:
            data['description'] = {
                'plain': {
                    'value': description,
                    'representation': 'plain'
                }
            }
        
        url = self.client._get_confluence_url('space')
        response = self.client._make_request('POST', url, self.client.confluence_auth, json=data)
        return response.json()
    
    def delete_space(self, space_key: str) -> bool:
        """Delete a space"""
        url = self.client._get_confluence_url(f'space/{space_key}')
        response = self.client._make_request('DELETE', url, self.client.confluence_auth)
        return response.status_code == 202  # Confluence returns 202 for async deletion
    
    def get_templates(self, space_key: str = None) -> List[Dict[str, Any]]:
        """Get page templates"""
        params = {}
        if space_key:
            params['spaceKey'] = space_key
        
        url = self.client._get_confluence_url('template/page')
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json().get('results', [])
    
    def create_page_from_template(self, space_key: str, title: str, template_id: str, 
                                 parent_id: str = None) -> Dict[str, Any]:
        """Create a page from a template"""
        data = {
            'type': 'page',
            'title': title,
            'space': {'key': space_key},
            'body': {
                'storage': {
                    'value': '',  # Will be populated from template
                    'representation': 'storage'
                }
            }
        }
        
        if parent_id:
            data['ancestors'] = [{'id': parent_id}]
        
        # First create the page, then apply template
        url = self.client._get_confluence_url('content')
        response = self.client._make_request('POST', url, self.client.confluence_auth, json=data)
        page = response.json()
        
        # Apply template (this is a simplified approach - real implementation would need template content)
        return page
    
    def get_analytics(self, content_id: str = None, space_key: str = None) -> Dict[str, Any]:
        """Get analytics data (if available)"""
        params = {}
        if content_id:
            params['contentId'] = content_id
        if space_key:
            params['spaceKey'] = space_key
        
        try:
            url = self.client._get_confluence_url('analytics/content/views')
            response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
            return response.json()
        except APIError as e:
            if e.status_code == 404:
                return {'message': 'Analytics not available'}
            raise
    
    def export_space(self, space_key: str, export_type: str = 'html') -> Dict[str, Any]:
        """Export space content"""
        data = {
            'exportType': export_type,
            'spaceKey': space_key
        }
        
        try:
            url = self.client._get_confluence_url('longtask')
            response = self.client._make_request('POST', url, self.client.confluence_auth, json=data)
            return response.json()
        except APIError as e:
            if e.status_code == 404:
                return {'message': 'Export functionality not available'}
            raise


class ExtendedJiraAPI(JiraAPI):
    """Extended Jira API with additional functionality for MCP"""

    def get_issue_transitions(self, issue_key: str) -> List[Dict[str, Any]]:
        """Get available transitions for an issue"""
        url = self.client._get_jira_url(f'issue/{issue_key}/transitions')
        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json().get('transitions', [])

    def transition_issue(self, issue_key: str, transition_id: str,
                        comment: str = None, fields: Dict[str, Any] = None) -> bool:
        """Transition an issue to a new status"""
        data = {
            'transition': {'id': transition_id}
        }

        if comment:
            data['update'] = {
                'comment': [{'add': {'body': comment}}]
            }

        if fields:
            data['fields'] = fields

        url = self.client._get_jira_url(f'issue/{issue_key}/transitions')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.status_code == 204

    def get_issue_watchers(self, issue_key: str) -> Dict[str, Any]:
        """Get watchers for an issue"""
        url = self.client._get_jira_url(f'issue/{issue_key}/watchers')
        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json()

    def add_watcher(self, issue_key: str, username: str) -> bool:
        """Add a watcher to an issue"""
        url = self.client._get_jira_url(f'issue/{issue_key}/watchers')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=username)
        return response.status_code == 204

    def remove_watcher(self, issue_key: str, username: str) -> bool:
        """Remove a watcher from an issue"""
        params = {'username': username}
        url = self.client._get_jira_url(f'issue/{issue_key}/watchers')
        response = self.client._make_request('DELETE', url, self.client.jira_auth, params=params)
        return response.status_code == 204

    def get_issue_votes(self, issue_key: str) -> Dict[str, Any]:
        """Get votes for an issue"""
        url = self.client._get_jira_url(f'issue/{issue_key}/votes')
        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json()

    def vote_issue(self, issue_key: str) -> bool:
        """Vote for an issue"""
        url = self.client._get_jira_url(f'issue/{issue_key}/votes')
        response = self.client._make_request('POST', url, self.client.jira_auth)
        return response.status_code == 204

    def unvote_issue(self, issue_key: str) -> bool:
        """Remove vote from an issue"""
        url = self.client._get_jira_url(f'issue/{issue_key}/votes')
        response = self.client._make_request('DELETE', url, self.client.jira_auth)
        return response.status_code == 204

    def get_issue_attachments(self, issue_key: str) -> List[Dict[str, Any]]:
        """Get attachments for an issue"""
        issue = self.get_issue(issue_key, fields=['attachment'])
        return issue.get('fields', {}).get('attachment', [])

    def add_attachment(self, issue_key: str, file_path: str) -> Dict[str, Any]:
        """Add an attachment to an issue"""
        import os

        filename = os.path.basename(file_path)

        with open(file_path, 'rb') as f:
            files = {'file': (filename, f, 'application/octet-stream')}
            headers = {
                'Authorization': self.client.jira_auth,
                'X-Atlassian-Token': 'no-check'
            }

            url = self.client._get_jira_url(f'issue/{issue_key}/attachments')
            response = self.client.session.post(url, files=files, headers=headers)

            if response.status_code >= 400:
                raise APIError(f"Attachment upload failed: {response.text}", response.status_code, response)

            return response.json()

    def delete_attachment(self, attachment_id: str) -> bool:
        """Delete an attachment"""
        url = self.client._get_jira_url(f'attachment/{attachment_id}')
        response = self.client._make_request('DELETE', url, self.client.jira_auth)
        return response.status_code == 204

    def get_issue_links(self, issue_key: str) -> List[Dict[str, Any]]:
        """Get issue links"""
        issue = self.get_issue(issue_key, fields=['issuelinks'])
        return issue.get('fields', {}).get('issuelinks', [])

    def link_issues(self, inward_issue: str, outward_issue: str,
                   link_type: str, comment: str = None) -> Dict[str, Any]:
        """Link two issues"""
        data = {
            'type': {'name': link_type},
            'inwardIssue': {'key': inward_issue},
            'outwardIssue': {'key': outward_issue}
        }

        if comment:
            data['comment'] = {'body': comment}

        url = self.client._get_jira_url('issueLink')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.json()

    def get_issue_link_types(self) -> List[Dict[str, Any]]:
        """Get available issue link types"""
        url = self.client._get_jira_url('issueLinkType')
        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json().get('issueLinkTypes', [])

    def get_project_versions(self, project_key: str) -> List[Dict[str, Any]]:
        """Get versions for a project"""
        url = self.client._get_jira_url(f'project/{project_key}/versions')
        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json()

    def create_version(self, project_key: str, name: str, description: str = None,
                      release_date: str = None, archived: bool = False) -> Dict[str, Any]:
        """Create a new version"""
        data = {
            'name': name,
            'project': project_key,
            'archived': archived
        }

        if description:
            data['description'] = description
        if release_date:
            data['releaseDate'] = release_date

        url = self.client._get_jira_url('version')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.json()

    def get_project_components(self, project_key: str) -> List[Dict[str, Any]]:
        """Get components for a project"""
        url = self.client._get_jira_url(f'project/{project_key}/components')
        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json()

    def create_component(self, project_key: str, name: str, description: str = None,
                        lead: str = None) -> Dict[str, Any]:
        """Create a new component"""
        data = {
            'name': name,
            'project': project_key
        }

        if description:
            data['description'] = description
        if lead:
            data['lead'] = {'name': lead}

        url = self.client._get_jira_url('component')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.json()

    def get_user_permissions(self, username: str = None) -> Dict[str, Any]:
        """Get user permissions"""
        params = {}
        if username:
            params['username'] = username

        url = self.client._get_jira_url('mypermissions')
        response = self.client._make_request('GET', url, self.client.jira_auth, params=params)
        return response.json()

    def get_current_user(self) -> Dict[str, Any]:
        """Get current user information"""
        url = self.client._get_jira_url('myself')
        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json()

    def search_users(self, query: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """Search for users"""
        params = {
            'query': query,
            'maxResults': max_results
        }

        url = self.client._get_jira_url('user/search')
        response = self.client._make_request('GET', url, self.client.jira_auth, params=params)
        return response.json()

    def get_issue_changelog(self, issue_key: str, start_at: int = 0,
                           max_results: int = 50) -> Dict[str, Any]:
        """Get issue changelog"""
        params = {
            'startAt': start_at,
            'maxResults': max_results,
            'expand': 'changelog'
        }

        url = self.client._get_jira_url(f'issue/{issue_key}/changelog')
        response = self.client._make_request('GET', url, self.client.jira_auth, params=params)
        return response.json()

    def get_issue_worklog(self, issue_key: str) -> List[Dict[str, Any]]:
        """Get worklog entries for an issue"""
        url = self.client._get_jira_url(f'issue/{issue_key}/worklog')
        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json().get('worklogs', [])

    def add_worklog(self, issue_key: str, time_spent: str, comment: str = None,
                   started: str = None) -> Dict[str, Any]:
        """Add a worklog entry to an issue"""
        data = {
            'timeSpent': time_spent
        }

        if comment:
            data['comment'] = comment
        if started:
            data['started'] = started
        else:
            data['started'] = datetime.now().strftime('%Y-%m-%dT%H:%M:%S.000+0000')

        url = self.client._get_jira_url(f'issue/{issue_key}/worklog')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.json()

    def get_dashboards(self, start_at: int = 0, max_results: int = 50) -> Dict[str, Any]:
        """Get user dashboards"""
        params = {
            'startAt': start_at,
            'maxResults': max_results
        }

        url = self.client._get_jira_url('dashboard')
        response = self.client._make_request('GET', url, self.client.jira_auth, params=params)
        return response.json()

    def get_filters(self, expand: str = None) -> List[Dict[str, Any]]:
        """Get user filters"""
        params = {}
        if expand:
            params['expand'] = expand

        url = self.client._get_jira_url('filter/favourite')
        response = self.client._make_request('GET', url, self.client.jira_auth, params=params)
        return response.json()

    def create_filter(self, name: str, jql: str, description: str = None,
                     favourite: bool = False) -> Dict[str, Any]:
        """Create a new filter"""
        data = {
            'name': name,
            'jql': jql,
            'favourite': favourite
        }

        if description:
            data['description'] = description

        url = self.client._get_jira_url('filter')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.json()

    def get_project_roles(self, project_key: str) -> Dict[str, Any]:
        """Get roles for a project"""
        url = self.client._get_jira_url(f'project/{project_key}/role')
        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json()

    def get_issue_security_levels(self, project_key: str) -> List[Dict[str, Any]]:
        """Get security levels for a project"""
        try:
            url = self.client._get_jira_url(f'project/{project_key}/securitylevel')
            response = self.client._make_request('GET', url, self.client.jira_auth)
            return response.json().get('levels', [])
        except APIError as e:
            if e.status_code == 404:
                return []
            raise

    def bulk_update_issues(self, issue_keys: List[str], fields: Dict[str, Any]) -> Dict[str, Any]:
        """Bulk update multiple issues"""
        data = {
            'issueUpdates': [
                {
                    'key': key,
                    'fields': fields
                } for key in issue_keys
            ]
        }

        url = self.client._get_jira_url('issue/bulk')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.json()


class ExtendedAtlassianClient(AtlassianClient):
    """Extended Atlassian client with enhanced APIs for MCP"""

    def __init__(self, config: Dict[str, str], **kwargs):
        super().__init__(config, **kwargs)
        self._confluence_api = None
        self._jira_api = None

    @property
    def confluence(self) -> ExtendedConfluenceAPI:
        """Get extended Confluence API instance"""
        if self._confluence_api is None:
            self._confluence_api = ExtendedConfluenceAPI(self)
        return self._confluence_api

    @property
    def jira(self) -> ExtendedJiraAPI:
        """Get extended Jira API instance"""
        if self._jira_api is None:
            self._jira_api = ExtendedJiraAPI(self)
        return self._jira_api

    def get_system_info(self) -> Dict[str, Any]:
        """Get system information for both services"""
        info = {}

        try:
            if self.confluence_auth:
                # Get Confluence system info
                confluence_info = self.confluence.get_user_details()
                info['confluence'] = {
                    'user': confluence_info,
                    'available': True
                }
        except Exception as e:
            info['confluence'] = {'available': False, 'error': str(e)}

        try:
            if self.jira_auth:
                # Get Jira system info
                jira_info = self.jira.get_server_info()
                current_user = self.jira.get_current_user()
                info['jira'] = {
                    'server': jira_info,
                    'user': current_user,
                    'available': True
                }
        except Exception as e:
            info['jira'] = {'available': False, 'error': str(e)}

        return info
