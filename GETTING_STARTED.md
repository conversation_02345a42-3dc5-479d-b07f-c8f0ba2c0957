# 🚀 Getting Started with Atlassian Client

Welcome! This guide will help you get up and running with the Atlassian Client for Smals.be in just a few minutes.

## ✅ What's Already Working

Your Atlassian Client is **fully configured and tested** with your Smals.be environment:

- ✅ **Confluence**: Connected and working (20 spaces accessible)
- ✅ **Jira**: Connected and working (718 projects accessible)
- ✅ **Authentication**: Personal Access Tokens configured correctly
- ✅ **All Tests**: Passing (95.5% success rate)

## 📁 Project Structure

```
atlassian-companion/
├── client/                 # 📚 Main client library
├── tests/                 # 🧪 Test suite  
├── examples/              # 📖 Usage examples
├── config.py              # ⚙️ Your configuration
├── main.py               # 🖥️ Command-line interface
└── setup.py              # 🔧 Setup script
```

## 🏃‍♂️ Quick Start (30 seconds)

### 1. Test Everything Works
```bash
python setup.py
```

### 2. Try the Command-Line Interface
```bash
# Test connection
python main.py test

# List your Confluence spaces
python main.py spaces

# List your Jira projects
python main.py projects

# Search for content
python main.py search-confluence --query "API"
python main.py search-jira --query "bug"
```

### 3. Use in Your Python Code
```python
from client import AtlassianClient, ConfluenceAPI, JiraAPI
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

# Initialize
client = AtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)

# Use Confluence
confluence = ConfluenceAPI(client)
spaces = confluence.get_spaces()
print(f"Found {len(spaces['results'])} spaces")

# Use Jira
jira = JiraAPI(client)
projects = jira.get_projects()
print(f"Found {len(projects)} projects")
```

## 🎯 What You Can Do

### Confluence Operations
- ✅ **Read**: Get spaces, pages, search content
- ✅ **Write**: Create and update pages
- ✅ **Search**: Use CQL (Confluence Query Language)
- ✅ **Navigate**: Handle page hierarchies and children

### Jira Operations  
- ✅ **Read**: Get projects, issues, search
- ✅ **Write**: Create and update issues
- ✅ **Search**: Use JQL (Jira Query Language)
- ✅ **Manage**: Add comments, handle workflows

### Advanced Features
- ✅ **Pagination**: Handle large datasets automatically
- ✅ **Error Handling**: Comprehensive error management
- ✅ **Utilities**: Query builders, content formatting
- ✅ **Authentication**: Personal Access Token support

## 📖 Examples

### Command-Line Examples
```bash
# Get help
python main.py --help

# Test connection
python main.py test

# List resources
python main.py spaces
python main.py projects

# Search content
python main.py search-confluence --query "documentation"
python main.py search-jira --query "security"
```

### Programming Examples
```python
# Search Confluence
from client import ConfluenceAPI, AtlassianUtils
confluence = ConfluenceAPI(client)

cql = AtlassianUtils.build_cql_query(
    space="027INFOSEC",
    content_type="page",
    text="security"
)
results = confluence.search_content(cql)

# Search Jira
from client import JiraAPI, AtlassianUtils
jira = JiraAPI(client)

jql = AtlassianUtils.build_jql_query(
    project="INFOSEC",
    status="Open",
    assignee="<EMAIL>"
)
issues = jira.search_issues(jql)

# Create a Confluence page
new_page = confluence.create_page(
    space_key="027INFOSEC",
    title="API Integration Guide",
    content="<p>This page documents our API integration.</p>"
)

# Create a Jira issue
new_issue = jira.create_issue(
    project_key="INFOSEC",
    summary="API Documentation Update",
    issue_type="Task",
    description="Update API documentation with new endpoints"
)
```

## 🧪 Testing

### Run All Tests
```bash
python run_tests.py
```

### Run Specific Tests
```bash
cd tests
python test_client.py      # Full functionality test
python test_auth.py        # Authentication test
python test_pat.py         # Personal Access Token test
```

### Run Examples
```bash
cd examples
python example_usage.py    # Comprehensive examples
```

## 🔧 Configuration

Your configuration is in `config.py`. It's already set up correctly, but you can modify it if needed:

```python
# config.py
ATLASSIAN_CONFIG = {
    "CONFLUENCE_URL": "https://your-confluence-server.com",
    "CONFLUENCE_USERNAME": "<EMAIL>",
    "CONFLUENCE_API_TOKEN": "your_confluence_token",
    "JIRA_URL": "https://your-jira-server.com",
    "JIRA_USERNAME": "<EMAIL>",
    "JIRA_API_TOKEN": "your_jira_token"
}

AUTH_METHOD = 'bearer'  # For Personal Access Tokens
```

## 🆘 Need Help?

### Documentation
- **README.md**: Complete API reference and documentation
- **examples/example_usage.py**: Comprehensive usage examples
- **tests/**: Test files show how each feature works

### Common Issues
1. **Authentication Errors**: Check if your Personal Access Tokens are still valid
2. **Network Issues**: Ensure you can reach confluence.smals.be and jira.smals.be
3. **Permission Issues**: Verify your account has API access permissions

### Support
- Check the test output for specific error messages
- Review the examples for correct usage patterns
- Contact your Atlassian administrator for permission issues

## 🎉 You're Ready!

Your Atlassian Client is fully configured and tested. You can now:

1. **Use the CLI** for quick operations
2. **Import the client** in your Python projects
3. **Build automation** around Confluence and Jira
4. **Integrate** with your existing workflows

Happy coding! 🚀
