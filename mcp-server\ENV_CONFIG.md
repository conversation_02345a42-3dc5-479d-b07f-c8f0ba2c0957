# Environment-Based Configuration for Atlassian MCP Server

## 🔧 **Configuration Methods**

The Atlassian MCP Server now supports **two configuration methods**:

### **Method 1: Environment Variables (Recommended)** ✅
- All configuration passed through MCP client environment
- No dependency on config.py files
- More secure and portable
- Easier to deploy across different environments

### **Method 2: File-Based (Fallback)** 
- Uses existing config.py file
- Maintains backward compatibility
- Requires config.py in parent directory

## 🌍 **Environment Variables**

### **Required Variables**

#### **Confluence Configuration**
```bash
CONFLUENCE_URL=https://your-confluence-server.com
CONFLUENCE_USERNAME=<EMAIL>
CONFLUENCE_API_TOKEN=your_confluence_personal_access_token
```

#### **Jira Configuration**
```bash
JIRA_URL=https://your-jira-server.com
JIRA_USERNAME=<EMAIL>
JIRA_API_TOKEN=your_jira_personal_access_token
```

### **Optional Variables**
```bash
ATLASSIAN_AUTH_METHOD=bearer          # Default: bearer
ATLASSIAN_VERIFY_SSL=true            # Default: true
ATLASSIAN_TIMEOUT=30                 # Default: 30 seconds
ATLASSIAN_MAX_RETRIES=3              # Default: 3 retries
```

## 📋 **MCP Client Configuration**

### **Complete Environment-Based Configuration**

```json
{
  "mcpServers": {
    "atlassian": {
      "command": "python",
      "args": [
        "C:\\dev\\prj\\atlassian-companion\\mcp-server\\server.py"
      ],
      "env": {
        "PYTHONPATH": "C:\\dev\\prj\\atlassian-companion",
        "CONFLUENCE_URL": "https://your-confluence-server.com",
        "CONFLUENCE_USERNAME": "<EMAIL>",
        "CONFLUENCE_API_TOKEN": "your_confluence_personal_access_token",
        "JIRA_URL": "https://your-jira-server.com",
        "JIRA_USERNAME": "<EMAIL>",
        "JIRA_API_TOKEN": "your_jira_personal_access_token",
        "ATLASSIAN_AUTH_METHOD": "bearer",
        "ATLASSIAN_VERIFY_SSL": "true",
        "ATLASSIAN_TIMEOUT": "30",
        "ATLASSIAN_MAX_RETRIES": "3"
      }
    }
  }
}
```

### **Minimal Configuration (File-Based Fallback)**

```json
{
  "mcpServers": {
    "atlassian": {
      "command": "python",
      "args": [
        "C:\\dev\\prj\\atlassian-companion\\mcp-server\\server.py"
      ],
      "env": {
        "PYTHONPATH": "C:\\dev\\prj\\atlassian-companion"
      }
    }
  }
}
```

## 🔒 **Security Benefits**

### **Environment-Based Approach**
- ✅ **Credentials in MCP client only** - not stored in files
- ✅ **No config.py dependency** - reduces attack surface
- ✅ **Easy credential rotation** - update MCP client config only
- ✅ **Environment isolation** - different configs per deployment
- ✅ **No accidental commits** - credentials not in code

### **File-Based Approach**
- ⚠️ **Credentials in config.py** - risk of accidental commits
- ⚠️ **File system dependency** - requires config.py file
- ⚠️ **Harder to rotate** - need to update files

## 🚀 **Deployment Scenarios**

### **Scenario 1: Claude Desktop (Personal)**
```json
{
  "mcpServers": {
    "atlassian": {
      "command": "python",
      "args": ["C:\\path\\to\\server.py"],
      "env": {
        "PYTHONPATH": "C:\\path\\to\\atlassian-companion",
        "CONFLUENCE_URL": "https://your-confluence-server.com",
        "CONFLUENCE_USERNAME": "<EMAIL>",
        "CONFLUENCE_API_TOKEN": "your_token_here",
        "JIRA_URL": "https://your-jira-server.com",
        "JIRA_USERNAME": "<EMAIL>",
        "JIRA_API_TOKEN": "your_token_here"
      }
    }
  }
}
```

### **Scenario 2: Production Server**
```json
{
  "mcpServers": {
    "atlassian": {
      "command": "/usr/bin/python3",
      "args": ["/opt/atlassian-mcp/server.py"],
      "env": {
        "PYTHONPATH": "/opt/atlassian-companion",
        "CONFLUENCE_URL": "${CONFLUENCE_URL}",
        "CONFLUENCE_USERNAME": "${CONFLUENCE_USERNAME}",
        "CONFLUENCE_API_TOKEN": "${CONFLUENCE_API_TOKEN}",
        "JIRA_URL": "${JIRA_URL}",
        "JIRA_USERNAME": "${JIRA_USERNAME}",
        "JIRA_API_TOKEN": "${JIRA_API_TOKEN}",
        "ATLASSIAN_VERIFY_SSL": "true",
        "ATLASSIAN_TIMEOUT": "60"
      }
    }
  }
}
```

### **Scenario 3: Development/Testing**
```json
{
  "mcpServers": {
    "atlassian": {
      "command": "python",
      "args": ["./server.py"],
      "env": {
        "PYTHONPATH": "..",
        "CONFLUENCE_URL": "https://your-confluence-dev-server.com",
        "CONFLUENCE_USERNAME": "<EMAIL>",
        "CONFLUENCE_API_TOKEN": "dev_token",
        "JIRA_URL": "https://your-jira-dev-server.com",
        "JIRA_USERNAME": "<EMAIL>",
        "JIRA_API_TOKEN": "dev_token",
        "ATLASSIAN_VERIFY_SSL": "false"
      }
    }
  }
}
```

## 🔄 **Migration Guide**

### **From File-Based to Environment-Based**

1. **Identify current config.py values**:
   ```python
   ATLASSIAN_CONFIG = {
       'CONFLUENCE_URL': 'https://your-confluence-server.com',
       'CONFLUENCE_USERNAME': '<EMAIL>',
       # ... etc
   }
   ```

2. **Convert to environment variables**:
   ```json
   "env": {
     "CONFLUENCE_URL": "https://your-confluence-server.com",
     "CONFLUENCE_USERNAME": "<EMAIL>",
     // ... etc
   }
   ```

3. **Test the configuration**:
   ```bash
   python test_mcp_server.py
   ```

4. **Remove config.py dependency** (optional):
   - Server will automatically use environment variables
   - Falls back to config.py if environment is incomplete

## ⚡ **Quick Setup**

1. **Run setup script**:
   ```bash
   cd mcp-server
   python setup_mcp.py
   ```

2. **Choose configuration file**:
   - `mcp_config_env.json` - Environment-based (recommended)
   - `mcp_config_basic.json` - File-based (fallback)

3. **Update credentials**:
   - Replace `your_*_personal_access_token` with actual tokens
   - Update usernames and URLs if needed

4. **Add to MCP client**:
   - Copy JSON content to your MCP client configuration

5. **Test**:
   ```bash
   python test_mcp_server.py
   ```

## 🎯 **Best Practices**

- ✅ **Use environment-based configuration** for production
- ✅ **Store tokens securely** in MCP client configuration
- ✅ **Use different tokens** for different environments
- ✅ **Enable SSL verification** in production (`ATLASSIAN_VERIFY_SSL=true`)
- ✅ **Set appropriate timeouts** for your network conditions
- ✅ **Test configuration** before deploying

---

**Recommendation**: Use the **environment-based configuration** for better security and portability! 🔒
