"""
Configuration file for Atlassian Client

This file contains the configuration settings for connecting to your
Atlassian on-premise installations.

IMPORTANT: Keep this file secure and do not commit it to version control
if it contains real credentials.
"""

# Atlassian Configuration Template
# Copy this template and set your actual values
ATLASSIAN_CONFIG = {
    # Confluence Configuration
    "CONFLUENCE_URL": "https://confluence.smals.be",
    "CONFLUENCE_USERNAME": "<EMAIL>",
    "CONFLUENCE_API_TOKEN": "MzI0MTY2MjY4NzE4OqFbCYdS9oFG",

    # Jira Configuration
    "JIRA_URL": "https://jira.smals.be",
    "JIRA_USERNAME": "<EMAIL>",
    "JIRA_API_TOKEN": "ODA3MDc2NTU4NzE4OqFbCYdWMAL"
}

# Authentication method for Personal Access Tokens
AUTH_METHOD = 'session'  # Use 'session' for session-based auth, 'bearer' for PATs, 'basic' for API tokens, 'auto' to detect

# Client settings
CLIENT_SETTINGS = {
    'timeout': 30,          # Request timeout in seconds
    'verify_ssl': True,     # Whether to verify SSL certificates
    'auth_method': AUTH_METHOD
}

# Example configuration template (copy and modify as needed)
CONFIG_TEMPLATE = {
    "CONFLUENCE_URL": "https://your-confluence-server.com",
    "CONFLUENCE_USERNAME": "<EMAIL>", 
    "CONFLUENCE_API_TOKEN": "your_confluence_personal_access_token",
    "JIRA_URL": "https://your-jira-server.com",
    "JIRA_USERNAME": "<EMAIL>",
    "JIRA_API_TOKEN": "your_jira_personal_access_token"
}
