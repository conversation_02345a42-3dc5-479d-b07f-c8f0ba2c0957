{"mcpServers": {"atlassian": {"command": "python", "args": ["C:\\dev\\prj\\atlassian-companion\\mcp-server\\server.py"], "env": {"PYTHONPATH": "C:\\dev\\prj\\atlassian-companion", "CONFLUENCE_URL": "https://confluence.smals.be", "CONFLUENCE_USERNAME": "<EMAIL>", "CONFLUENCE_API_TOKEN": "not-needed-for-session-auth", "JIRA_URL": "https://jira.smals.be", "JIRA_USERNAME": "<EMAIL>", "JIRA_API_TOKEN": "not-needed-for-session-auth", "ATLASSIAN_AUTH_METHOD": "session", "ATLASSIAN_VERIFY_SSL": "true", "ATLASSIAN_TIMEOUT": "30", "ATLASSIAN_MAX_RETRIES": "3", "CONFLUENCE_BROWSER_COOKIE": "826908A1CFAD61EB7F4C1FC7B5BDE952", "JIRA_BROWSER_COOKIE": "F336256A0514E2187297593B24B52819"}}}}