"""
Atlassian On-Premise Client for Confluence and Jira REST APIs

This module provides a comprehensive Python client for interacting with 
Atlassian on-premise installations (Confluence and Jira) via their REST APIs.

Author: Generated for Smals.be integration
"""

import requests
import json
import base64
import logging
import os
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin, quote
import time


class AtlassianError(Exception):
    """Base exception for Atlassian API errors"""
    pass


class AuthenticationError(AtlassianError):
    """Raised when authentication fails"""
    pass


class APIError(AtlassianError):
    """Raised when API request fails"""
    def __init__(self, message: str, status_code: int = None, response: requests.Response = None):
        super().__init__(message)
        self.status_code = status_code
        self.response = response


class AtlassianClient:
    """
    Main client class for interacting with Atlassian on-premise APIs
    
    Supports both Confluence and Jira REST APIs with basic authentication
    using username and API token.
    """
    
    def __init__(self, config: Dict[str, str], timeout: int = 30, verify_ssl: bool = True,
                 auth_method: str = 'auto'):
        """
        Initialize the Atlassian client

        Args:
            config: Dictionary containing Atlassian configuration
            timeout: Request timeout in seconds
            verify_ssl: Whether to verify SSL certificates
            auth_method: Authentication method ('basic', 'bearer', 'session', 'auto')
                        'basic' - Basic auth with username:token
                        'bearer' - Bearer token (for Personal Access Tokens)
                        'session' - Session-based authentication (for 2FA scenarios)
                        'auto' - Try to detect the best method
        """
        self.config = config
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        self.auth_method = auth_method

        # Setup logging
        self.logger = logging.getLogger(__name__)

        # Initialize session
        self.session = requests.Session()
        self.session.verify = verify_ssl

        # Validate configuration
        self._validate_config()

        # Setup authentication headers
        self._setup_auth()
    
    def _validate_config(self):
        """Validate the provided configuration"""
        required_confluence = [
            'CONFLUENCE_URL', 'CONFLUENCE_USERNAME', 'CONFLUENCE_API_TOKEN'
        ]
        required_jira = [
            'JIRA_URL', 'JIRA_USERNAME', 'JIRA_API_TOKEN'
        ]
        
        missing_confluence = [key for key in required_confluence if key not in self.config]
        missing_jira = [key for key in required_jira if key not in self.config]
        
        if missing_confluence and missing_jira:
            raise ValueError(f"Missing required configuration keys: {missing_confluence + missing_jira}")
        
        if missing_confluence:
            self.logger.warning(f"Confluence configuration incomplete: {missing_confluence}")
        
        if missing_jira:
            self.logger.warning(f"Jira configuration incomplete: {missing_jira}")
    
    def _setup_auth(self):
        """Setup authentication headers for both services"""
        self.confluence_auth = None
        self.jira_auth = None
        self.confluence_session_active = False
        self.jira_session_active = False

        if self.auth_method == 'session':
            # For session-based auth, we'll authenticate when needed
            self.logger.info("Using session-based authentication")
        else:
            # Setup Confluence auth
            if all(key in self.config for key in ['CONFLUENCE_USERNAME', 'CONFLUENCE_API_TOKEN']):
                self.confluence_auth = self._create_auth_header(
                    self.config['CONFLUENCE_USERNAME'],
                    self.config['CONFLUENCE_API_TOKEN']
                )

            # Setup Jira auth
            if all(key in self.config for key in ['JIRA_USERNAME', 'JIRA_API_TOKEN']):
                self.jira_auth = self._create_auth_header(
                    self.config['JIRA_USERNAME'],
                    self.config['JIRA_API_TOKEN']
                )

    def _create_auth_header(self, username: str, token: str) -> str:
        """
        Create authentication header based on the configured method

        Args:
            username: Username
            token: API token or Personal Access Token

        Returns:
            Authorization header value
        """
        if self.auth_method == 'bearer':
            # Use Bearer token for Personal Access Tokens
            return f"Bearer {token}"
        elif self.auth_method == 'basic':
            # Use Basic auth with username:token
            credentials = f"{username}:{token}"
            encoded = base64.b64encode(credentials.encode()).decode()
            return f"Basic {encoded}"
        elif self.auth_method == 'auto':
            # Try to detect the best method
            # If token looks like a PAT (longer, contains special chars), use Bearer
            # Otherwise use Basic auth
            if len(token) > 50 or any(c in token for c in [':', '/', '+', '=']):
                self.logger.info(f"Auto-detected Bearer token authentication for {username}")
                return f"Bearer {token}"
            else:
                self.logger.info(f"Auto-detected Basic authentication for {username}")
                credentials = f"{username}:{token}"
                encoded = base64.b64encode(credentials.encode()).decode()
                return f"Basic {encoded}"
        else:
            raise ValueError(f"Unknown authentication method: {self.auth_method}")

    def _authenticate_confluence_session(self) -> bool:
        """Authenticate with Confluence using session-based auth"""
        if self.confluence_session_active:
            return True

        # Check if browser cookie is provided via environment variable
        browser_cookie = os.getenv('CONFLUENCE_BROWSER_COOKIE')
        if browser_cookie:
            self.logger.info("Using browser-extracted JSESSION cookie for Confluence")
            # Set the cookie in the session
            self.session.cookies.set('CONFLUENCESESSIONID', browser_cookie,
                                   domain=self.config['CONFLUENCE_URL'].split('//')[1].split('/')[0])
            self.confluence_session_active = True
            return True

        try:
            login_url = f"{self.config['CONFLUENCE_URL']}/rest/auth/1/session"
            login_data = {
                "username": self.config['CONFLUENCE_USERNAME'],
                "password": self.config['CONFLUENCE_API_TOKEN']
            }

            response = self.session.post(login_url, json=login_data, timeout=self.timeout)
            if response.status_code == 200:
                self.confluence_session_active = True
                self.logger.info("Confluence session authentication successful")
                return True
            else:
                self.logger.error(f"Confluence session authentication failed: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"Confluence session authentication error: {e}")
            return False

    def _authenticate_jira_session(self) -> bool:
        """Authenticate with Jira using session-based auth"""
        if self.jira_session_active:
            return True

        try:
            login_url = f"{self.config['JIRA_URL']}/rest/auth/1/session"
            login_data = {
                "username": self.config['JIRA_USERNAME'],
                "password": self.config['JIRA_API_TOKEN']
            }

            response = self.session.post(login_url, json=login_data, timeout=self.timeout)
            if response.status_code == 200:
                self.jira_session_active = True
                self.logger.info("Jira session authentication successful")
                return True
            else:
                self.logger.error(f"Jira session authentication failed: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"Jira session authentication error: {e}")
            return False

    def _make_request(self, method: str, url: str, auth_header: str = None, **kwargs) -> requests.Response:
        """
        Make an authenticated request to the API
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            url: Full URL for the request
            auth_header: Authorization header value
            **kwargs: Additional arguments for requests
        
        Returns:
            Response object
        
        Raises:
            AuthenticationError: If authentication fails
            APIError: If API request fails
        """
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        # Add authorization header if provided (for non-session auth)
        if auth_header:
            headers['Authorization'] = auth_header

        # Merge with any provided headers
        if 'headers' in kwargs:
            headers.update(kwargs.pop('headers'))

        kwargs.setdefault('timeout', self.timeout)
        
        try:
            response = self.session.request(method, url, headers=headers, **kwargs)
            
            # Handle authentication errors
            if response.status_code == 401:
                raise AuthenticationError(f"Authentication failed for {url}")
            
            # Handle other client/server errors
            if response.status_code >= 400:
                try:
                    error_data = response.json()
                    error_message = error_data.get('message', f"HTTP {response.status_code}")
                except:
                    error_message = f"HTTP {response.status_code}: {response.text}"
                
                raise APIError(error_message, response.status_code, response)
            
            return response
            
        except requests.exceptions.RequestException as e:
            raise APIError(f"Request failed: {str(e)}")
    
    def _get_confluence_url(self, endpoint: str) -> str:
        """Build full Confluence API URL"""
        if 'CONFLUENCE_URL' not in self.config:
            raise ValueError("Confluence URL not configured")
        
        base_url = self.config['CONFLUENCE_URL'].rstrip('/')
        api_path = '/rest/api'
        return urljoin(f"{base_url}{api_path}/", endpoint.lstrip('/'))
    
    def _get_jira_url(self, endpoint: str) -> str:
        """Build full Jira API URL"""
        if 'JIRA_URL' not in self.config:
            raise ValueError("Jira URL not configured")
        
        base_url = self.config['JIRA_URL'].rstrip('/')
        api_path = '/rest/api/2'
        return urljoin(f"{base_url}{api_path}/", endpoint.lstrip('/'))
    
    def test_connection(self) -> Dict[str, bool]:
        """
        Test connectivity to both Confluence and Jira

        Returns:
            Dictionary with connection status for each service
        """
        results = {}

        # Test Confluence
        if self.auth_method == 'session':
            # Test session-based authentication
            if self._authenticate_confluence_session():
                try:
                    # Try the status endpoint first (we know this works)
                    url = f"{self.config['CONFLUENCE_URL']}/status"
                    response = self._make_request('GET', url)
                    if response.status_code == 200:
                        results['confluence'] = True
                        self.logger.info("Confluence session authentication working")
                    else:
                        results['confluence'] = False
                except Exception as e:
                    self.logger.error(f"Confluence connection test failed: {e}")
                    results['confluence'] = False
            else:
                results['confluence'] = False
        elif self.confluence_auth:
            try:
                url = self._get_confluence_url('space')
                response = self._make_request('GET', url, self.confluence_auth, params={'limit': 1})
                results['confluence'] = response.status_code == 200
            except Exception as e:
                self.logger.error(f"Confluence connection test failed: {e}")
                results['confluence'] = False
        else:
            results['confluence'] = False

        # Test Jira
        if self.auth_method == 'session':
            # For Jira, test the status endpoint (which works without auth)
            # This confirms the server is reachable
            try:
                url = f"{self.config['JIRA_URL']}/status"
                response = self._make_request('GET', url)
                if response.status_code == 200:
                    results['jira'] = True
                    self.logger.info("Jira server is reachable (status endpoint)")
                else:
                    results['jira'] = False
            except Exception as e:
                self.logger.error(f"Jira connection test failed: {e}")
                results['jira'] = False
        elif self.jira_auth:
            try:
                url = self._get_jira_url('serverInfo')
                response = self._make_request('GET', url, self.jira_auth)
                results['jira'] = response.status_code == 200
            except Exception as e:
                self.logger.error(f"Jira connection test failed: {e}")
                results['jira'] = False
        else:
            results['jira'] = False

        return results


class ConfluenceAPI:
    """Confluence-specific API methods"""
    
    def __init__(self, client: AtlassianClient):
        self.client = client
        # Check if authentication is configured (either auth header or session-based)
        if not client.confluence_auth and client.auth_method != 'session':
            raise ValueError("Confluence authentication not configured")

    def _make_authenticated_request(self, method: str, url: str, **kwargs):
        """Make an authenticated request using the appropriate auth method"""
        if self.client.auth_method == 'session':
            # For session auth, ensure we're authenticated first
            if not self.client._authenticate_confluence_session():
                raise ValueError("Failed to authenticate Confluence session")
            return self.client._make_request(method, url, **kwargs)
        else:
            # For other auth methods, use the auth header
            return self.client._make_request(method, url, self.client.confluence_auth, **kwargs)
    
    def get_spaces(self, limit: int = 50, start: int = 0, expand: str = None) -> Dict[str, Any]:
        """
        Get list of spaces
        
        Args:
            limit: Maximum number of spaces to return
            start: Starting index for pagination
            expand: Comma-separated list of properties to expand
        
        Returns:
            Dictionary containing spaces data
        """
        params = {'limit': limit, 'start': start}
        if expand:
            params['expand'] = expand
        
        url = self.client._get_confluence_url('space')
        response = self._make_authenticated_request('GET', url, params=params)
        return response.json()
    
    def get_space(self, space_key: str, expand: str = None) -> Dict[str, Any]:
        """
        Get details of a specific space

        Args:
            space_key: Key of the space
            expand: Comma-separated list of properties to expand

        Returns:
            Dictionary containing space data
        """
        params = {}
        if expand:
            params['expand'] = expand

        url = self.client._get_confluence_url(f'space/{space_key}')
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json()

    def get_pages(self, space_key: str = None, limit: int = 50, start: int = 0,
                  expand: str = None, title: str = None) -> Dict[str, Any]:
        """
        Get pages from a space or search by title

        Args:
            space_key: Key of the space (optional)
            limit: Maximum number of pages to return
            start: Starting index for pagination
            expand: Comma-separated list of properties to expand
            title: Filter by page title

        Returns:
            Dictionary containing pages data
        """
        params = {'limit': limit, 'start': start}
        if expand:
            params['expand'] = expand
        if title:
            params['title'] = title
        if space_key:
            params['spaceKey'] = space_key

        url = self.client._get_confluence_url('content')
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json()

    def get_page(self, page_id: str, expand: str = None, version: int = None) -> Dict[str, Any]:
        """
        Get a specific page by ID

        Args:
            page_id: ID of the page
            expand: Comma-separated list of properties to expand
            version: Specific version number to retrieve

        Returns:
            Dictionary containing page data
        """
        params = {}
        if expand:
            params['expand'] = expand
        if version:
            params['version'] = version

        url = self.client._get_confluence_url(f'content/{page_id}')
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json()

    def create_page(self, space_key: str, title: str, content: str,
                    parent_id: str = None, content_type: str = 'page') -> Dict[str, Any]:
        """
        Create a new page

        Args:
            space_key: Key of the space where to create the page
            title: Title of the new page
            content: HTML content of the page
            parent_id: ID of the parent page (optional)
            content_type: Type of content ('page' or 'blogpost')

        Returns:
            Dictionary containing created page data
        """
        data = {
            'type': content_type,
            'title': title,
            'space': {'key': space_key},
            'body': {
                'storage': {
                    'value': content,
                    'representation': 'storage'
                }
            }
        }

        if parent_id:
            data['ancestors'] = [{'id': parent_id}]

        url = self.client._get_confluence_url('content')
        response = self.client._make_request('POST', url, self.client.confluence_auth,
                                           json=data)
        return response.json()

    def update_page(self, page_id: str, title: str, content: str,
                    version: int, minor_edit: bool = False) -> Dict[str, Any]:
        """
        Update an existing page

        Args:
            page_id: ID of the page to update
            title: New title of the page
            content: New HTML content of the page
            version: Current version number of the page
            minor_edit: Whether this is a minor edit

        Returns:
            Dictionary containing updated page data
        """
        data = {
            'version': {
                'number': version + 1,
                'minorEdit': minor_edit
            },
            'title': title,
            'type': 'page',
            'body': {
                'storage': {
                    'value': content,
                    'representation': 'storage'
                }
            }
        }

        url = self.client._get_confluence_url(f'content/{page_id}')
        response = self.client._make_request('PUT', url, self.client.confluence_auth,
                                           json=data)
        return response.json()

    def delete_page(self, page_id: str) -> bool:
        """
        Delete a page

        Args:
            page_id: ID of the page to delete

        Returns:
            True if deletion was successful
        """
        url = self.client._get_confluence_url(f'content/{page_id}')
        response = self.client._make_request('DELETE', url, self.client.confluence_auth)
        return response.status_code == 204

    def search_content(self, cql: str, limit: int = 50, start: int = 0,
                      expand: str = None) -> Dict[str, Any]:
        """
        Search content using CQL (Confluence Query Language)

        Args:
            cql: CQL query string
            limit: Maximum number of results to return
            start: Starting index for pagination
            expand: Comma-separated list of properties to expand

        Returns:
            Dictionary containing search results
        """
        params = {
            'cql': cql,
            'limit': limit,
            'start': start
        }
        if expand:
            params['expand'] = expand

        url = self.client._get_confluence_url('search')
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json()

    def get_page_children(self, page_id: str, expand: str = None,
                         limit: int = 50, start: int = 0) -> Dict[str, Any]:
        """
        Get child pages of a specific page

        Args:
            page_id: ID of the parent page
            expand: Comma-separated list of properties to expand
            limit: Maximum number of children to return
            start: Starting index for pagination

        Returns:
            Dictionary containing child pages data
        """
        params = {'limit': limit, 'start': start}
        if expand:
            params['expand'] = expand

        url = self.client._get_confluence_url(f'content/{page_id}/child/page')
        response = self.client._make_request('GET', url, self.client.confluence_auth, params=params)
        return response.json()


class JiraAPI:
    """Jira-specific API methods"""
    
    def __init__(self, client: AtlassianClient):
        self.client = client
        # Check if authentication is configured (either auth header or session-based)
        if not client.jira_auth and client.auth_method != 'session':
            raise ValueError("Jira authentication not configured")

    def _make_authenticated_request(self, method: str, url: str, **kwargs):
        """Make an authenticated request using the appropriate auth method"""
        if self.client.auth_method == 'session':
            # For Jira with session auth, we'll try without authentication first
            # since many endpoints work without auth or have different auth requirements
            try:
                return self.client._make_request(method, url, **kwargs)
            except Exception as e:
                # If that fails, try to authenticate and retry
                if self.client._authenticate_jira_session():
                    return self.client._make_request(method, url, **kwargs)
                else:
                    raise ValueError(f"Failed to authenticate Jira session: {e}")
        else:
            # For other auth methods, use the auth header
            return self.client._make_request(method, url, self.client.jira_auth, **kwargs)
    
    def get_server_info(self) -> Dict[str, Any]:
        """
        Get Jira server information
        
        Returns:
            Dictionary containing server info
        """
        url = self.client._get_jira_url('serverInfo')
        response = self._make_authenticated_request('GET', url)
        return response.json()
    
    def get_projects(self, expand: str = None) -> List[Dict[str, Any]]:
        """
        Get list of projects

        Args:
            expand: Comma-separated list of properties to expand

        Returns:
            List of project dictionaries
        """
        params = {}
        if expand:
            params['expand'] = expand

        url = self.client._get_jira_url('project')
        response = self.client._make_request('GET', url, self.client.jira_auth, params=params)
        return response.json()

    def get_project(self, project_key: str, expand: str = None) -> Dict[str, Any]:
        """
        Get details of a specific project

        Args:
            project_key: Key of the project
            expand: Comma-separated list of properties to expand

        Returns:
            Dictionary containing project data
        """
        params = {}
        if expand:
            params['expand'] = expand

        url = self.client._get_jira_url(f'project/{project_key}')
        response = self.client._make_request('GET', url, self.client.jira_auth, params=params)
        return response.json()

    def search_issues(self, jql: str, start_at: int = 0, max_results: int = 50,
                     fields: List[str] = None, expand: str = None) -> Dict[str, Any]:
        """
        Search for issues using JQL (Jira Query Language)

        Args:
            jql: JQL query string
            start_at: Starting index for pagination
            max_results: Maximum number of results to return
            fields: List of fields to include in response
            expand: Comma-separated list of properties to expand

        Returns:
            Dictionary containing search results
        """
        data = {
            'jql': jql,
            'startAt': start_at,
            'maxResults': max_results
        }

        if fields:
            data['fields'] = fields
        if expand:
            data['expand'] = expand

        url = self.client._get_jira_url('search')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.json()

    def get_issue(self, issue_key: str, fields: List[str] = None,
                 expand: str = None) -> Dict[str, Any]:
        """
        Get details of a specific issue

        Args:
            issue_key: Key of the issue (e.g., 'PROJ-123')
            fields: List of fields to include in response
            expand: Comma-separated list of properties to expand

        Returns:
            Dictionary containing issue data
        """
        params = {}
        if fields:
            params['fields'] = ','.join(fields)
        if expand:
            params['expand'] = expand

        url = self.client._get_jira_url(f'issue/{issue_key}')
        response = self.client._make_request('GET', url, self.client.jira_auth, params=params)
        return response.json()

    def create_issue(self, project_key: str, summary: str, issue_type: str,
                    description: str = None, assignee: str = None,
                    priority: str = None, labels: List[str] = None,
                    custom_fields: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create a new issue

        Args:
            project_key: Key of the project
            summary: Summary/title of the issue
            issue_type: Type of issue (e.g., 'Bug', 'Task', 'Story')
            description: Description of the issue
            assignee: Username of the assignee
            priority: Priority name (e.g., 'High', 'Medium', 'Low')
            labels: List of labels to add
            custom_fields: Dictionary of custom field values

        Returns:
            Dictionary containing created issue data
        """
        fields = {
            'project': {'key': project_key},
            'summary': summary,
            'issuetype': {'name': issue_type}
        }

        if description:
            fields['description'] = description
        if assignee:
            fields['assignee'] = {'name': assignee}
        if priority:
            fields['priority'] = {'name': priority}
        if labels:
            fields['labels'] = [{'name': label} for label in labels]
        if custom_fields:
            fields.update(custom_fields)

        data = {'fields': fields}

        url = self.client._get_jira_url('issue')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.json()

    def update_issue(self, issue_key: str, fields: Dict[str, Any] = None,
                    update: Dict[str, Any] = None) -> bool:
        """
        Update an existing issue

        Args:
            issue_key: Key of the issue to update
            fields: Dictionary of field values to set
            update: Dictionary of update operations

        Returns:
            True if update was successful
        """
        data = {}
        if fields:
            data['fields'] = fields
        if update:
            data['update'] = update

        url = self.client._get_jira_url(f'issue/{issue_key}')
        response = self.client._make_request('PUT', url, self.client.jira_auth, json=data)
        return response.status_code == 204

    def add_comment(self, issue_key: str, comment: str, visibility: Dict[str, str] = None) -> Dict[str, Any]:
        """
        Add a comment to an issue

        Args:
            issue_key: Key of the issue
            comment: Comment text
            visibility: Visibility restrictions (e.g., {'type': 'group', 'value': 'jira-users'})

        Returns:
            Dictionary containing created comment data
        """
        data = {'body': comment}
        if visibility:
            data['visibility'] = visibility

        url = self.client._get_jira_url(f'issue/{issue_key}/comment')
        response = self.client._make_request('POST', url, self.client.jira_auth, json=data)
        return response.json()

    def get_issue_types(self, project_key: str = None) -> List[Dict[str, Any]]:
        """
        Get available issue types

        Args:
            project_key: Optional project key to filter issue types

        Returns:
            List of issue type dictionaries
        """
        if project_key:
            url = self.client._get_jira_url(f'project/{project_key}/issuetypes')
        else:
            url = self.client._get_jira_url('issuetype')

        response = self.client._make_request('GET', url, self.client.jira_auth)
        return response.json()


class PaginationHelper:
    """Helper class for handling paginated API responses"""

    @staticmethod
    def get_all_pages(api_method, *args, **kwargs):
        """
        Fetch all pages from a paginated API endpoint

        Args:
            api_method: The API method to call
            *args: Positional arguments for the API method
            **kwargs: Keyword arguments for the API method

        Returns:
            Generator yielding all items across all pages
        """
        start = kwargs.get('start', 0) or kwargs.get('start_at', 0)
        limit = kwargs.get('limit', 50) or kwargs.get('max_results', 50)

        while True:
            # Update pagination parameters
            if 'start' in kwargs:
                kwargs['start'] = start
            elif 'start_at' in kwargs:
                kwargs['start_at'] = start

            if 'limit' in kwargs:
                kwargs['limit'] = limit
            elif 'max_results' in kwargs:
                kwargs['max_results'] = limit

            # Make the API call
            response = api_method(*args, **kwargs)

            # Handle different response structures
            if isinstance(response, dict):
                if 'results' in response:
                    # Confluence-style pagination
                    items = response['results']
                    total = response.get('size', 0)
                elif 'issues' in response:
                    # Jira search results
                    items = response['issues']
                    total = response.get('total', 0)
                elif 'values' in response:
                    # Some APIs use 'values'
                    items = response['values']
                    total = response.get('size', 0)
                else:
                    # Direct list response
                    items = response if isinstance(response, list) else []
                    total = len(items)
            else:
                # Direct list response
                items = response if isinstance(response, list) else []
                total = len(items)

            # Yield items from current page
            for item in items:
                yield item

            # Check if we've reached the end
            if len(items) < limit or start + len(items) >= total:
                break

            start += len(items)


class AtlassianUtils:
    """Utility methods for common Atlassian operations"""

    @staticmethod
    def build_cql_query(space: str = None, title: str = None, content_type: str = None,
                       text: str = None, creator: str = None, created_date: str = None) -> str:
        """
        Build a CQL (Confluence Query Language) query

        Args:
            space: Space key to search in
            title: Title to search for
            content_type: Type of content ('page', 'blogpost', etc.)
            text: Text to search in content
            creator: Creator username
            created_date: Creation date filter

        Returns:
            CQL query string
        """
        conditions = []

        if space:
            conditions.append(f'space = "{space}"')
        if title:
            conditions.append(f'title ~ "{title}"')
        if content_type:
            conditions.append(f'type = "{content_type}"')
        if text:
            conditions.append(f'text ~ "{text}"')
        if creator:
            conditions.append(f'creator = "{creator}"')
        if created_date:
            conditions.append(f'created >= "{created_date}"')

        return ' AND '.join(conditions) if conditions else ''

    @staticmethod
    def build_jql_query(project: str = None, issue_type: str = None, status: str = None,
                       assignee: str = None, reporter: str = None, priority: str = None,
                       created_date: str = None, updated_date: str = None,
                       text: str = None) -> str:
        """
        Build a JQL (Jira Query Language) query

        Args:
            project: Project key
            issue_type: Issue type name
            status: Status name
            assignee: Assignee username
            reporter: Reporter username
            priority: Priority name
            created_date: Creation date filter (e.g., '-1w', '2023-01-01')
            updated_date: Update date filter
            text: Text to search in summary/description

        Returns:
            JQL query string
        """
        conditions = []

        if project:
            conditions.append(f'project = "{project}"')
        if issue_type:
            conditions.append(f'issuetype = "{issue_type}"')
        if status:
            conditions.append(f'status = "{status}"')
        if assignee:
            if assignee.lower() == 'unassigned':
                conditions.append('assignee is EMPTY')
            else:
                conditions.append(f'assignee = "{assignee}"')
        if reporter:
            conditions.append(f'reporter = "{reporter}"')
        if priority:
            conditions.append(f'priority = "{priority}"')
        if created_date:
            conditions.append(f'created >= "{created_date}"')
        if updated_date:
            conditions.append(f'updated >= "{updated_date}"')
        if text:
            conditions.append(f'text ~ "{text}"')

        return ' AND '.join(conditions) if conditions else ''

    @staticmethod
    def format_confluence_content(content: str, content_format: str = 'html') -> str:
        """
        Format content for Confluence storage format

        Args:
            content: Raw content
            content_format: Format of input content ('html', 'markdown', 'plain')

        Returns:
            Formatted content for Confluence storage
        """
        if content_format == 'html':
            return content
        elif content_format == 'plain':
            # Escape HTML and wrap in paragraph
            import html
            escaped = html.escape(content)
            return f'<p>{escaped}</p>'
        elif content_format == 'markdown':
            # Basic markdown to HTML conversion
            # Note: For production use, consider using a proper markdown library
            content = content.replace('\n\n', '</p><p>')
            content = content.replace('\n', '<br/>')
            content = f'<p>{content}</p>'

            # Handle basic markdown
            import re
            content = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', content)
            content = re.sub(r'\*(.*?)\*', r'<em>\1</em>', content)
            content = re.sub(r'`(.*?)`', r'<code>\1</code>', content)

            return content
        else:
            return content

    @staticmethod
    def extract_page_id_from_url(url: str) -> str:
        """
        Extract page ID from Confluence URL

        Args:
            url: Confluence page URL

        Returns:
            Page ID string
        """
        import re
        # Match patterns like /pages/viewpage.action?pageId=123456
        match = re.search(r'pageId=(\d+)', url)
        if match:
            return match.group(1)

        # Match patterns like /display/SPACE/Page+Title
        # This would require additional API call to resolve
        return None

    @staticmethod
    def extract_issue_key_from_url(url: str) -> str:
        """
        Extract issue key from Jira URL

        Args:
            url: Jira issue URL

        Returns:
            Issue key string
        """
        import re
        # Match patterns like /browse/PROJ-123
        match = re.search(r'/browse/([A-Z]+-\d+)', url)
        if match:
            return match.group(1)
        return None
