#!/usr/bin/env python3
"""
Atlassian MCP Server

A Model Context Protocol (MCP) server that provides comprehensive access to
Atlassian on-premise installations (Confluence and Jira) via their REST APIs.

This server exposes Atlassian functionality as MCP tools and resources,
allowing AI assistants to interact with <PERSON>fluence and <PERSON><PERSON> seamlessly.
"""

import asyncio
import json
import logging
import sys
import os
from typing import Any, Dict, List, Optional, Sequence
from urllib.parse import urlparse, parse_qs

# Add parent directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from mcp.server import Server, NotificationOptions
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource, Tool, TextContent, ImageContent, EmbeddedResource,
    CallToolRequest, CallToolResult, ReadResourceRequest, ReadResourceResult,
    ListResourcesRequest, ListResourcesResult,
    ListToolsRequest, ListToolsResult, TextResourceContents
)

from extended_atlassian_client import ExtendedAtlassianClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global client instance
atlassian_client: Optional[ExtendedAtlassianClient] = None


def get_config_from_env() -> Dict[str, str]:
    """Get Atlassian configuration from environment variables"""
    config = {}

    # Required Confluence settings
    confluence_url = os.getenv('CONFLUENCE_URL')
    confluence_username = os.getenv('CONFLUENCE_USERNAME')
    confluence_token = os.getenv('CONFLUENCE_API_TOKEN')

    if confluence_url and confluence_username and confluence_token:
        config.update({
            'CONFLUENCE_URL': confluence_url,
            'CONFLUENCE_USERNAME': confluence_username,
            'CONFLUENCE_API_TOKEN': confluence_token
        })

    # Required Jira settings
    jira_url = os.getenv('JIRA_URL')
    jira_username = os.getenv('JIRA_USERNAME')
    jira_token = os.getenv('JIRA_API_TOKEN')

    if jira_url and jira_username and jira_token:
        config.update({
            'JIRA_URL': jira_url,
            'JIRA_USERNAME': jira_username,
            'JIRA_API_TOKEN': jira_token
        })

    return config


def get_client_settings_from_env() -> Dict[str, Any]:
    """Get client settings from environment variables"""
    settings = {}

    # Optional settings with defaults
    if os.getenv('ATLASSIAN_AUTH_METHOD'):
        settings['auth_method'] = os.getenv('ATLASSIAN_AUTH_METHOD')

    if os.getenv('ATLASSIAN_VERIFY_SSL'):
        settings['verify_ssl'] = os.getenv('ATLASSIAN_VERIFY_SSL').lower() == 'true'

    if os.getenv('ATLASSIAN_TIMEOUT'):
        try:
            settings['timeout'] = int(os.getenv('ATLASSIAN_TIMEOUT'))
        except ValueError:
            pass

    # Note: max_retries is not supported by the base client
    # if os.getenv('ATLASSIAN_MAX_RETRIES'):
    #     try:
    #         settings['max_retries'] = int(os.getenv('ATLASSIAN_MAX_RETRIES'))
    #     except ValueError:
    #         pass

    return settings


def get_client() -> ExtendedAtlassianClient:
    """Get or create the Atlassian client instance"""
    global atlassian_client
    if atlassian_client is None:
        # Try to get config from environment first
        config = get_config_from_env()
        settings = get_client_settings_from_env()

        # Fallback to file-based config if environment is not complete
        if not config:
            try:
                from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS
                config = ATLASSIAN_CONFIG
                if not settings:
                    settings = CLIENT_SETTINGS
                logger.info("Using file-based configuration")
            except ImportError:
                raise ValueError(
                    "No configuration found. Please set environment variables or create config.py:\n"
                    "Required: CONFLUENCE_URL, CONFLUENCE_USERNAME, CONFLUENCE_API_TOKEN, "
                    "JIRA_URL, JIRA_USERNAME, JIRA_API_TOKEN"
                )
        else:
            logger.info("Using environment-based configuration")

        atlassian_client = ExtendedAtlassianClient(config, **settings)
    return atlassian_client


async def enhanced_test_connection(client: ExtendedAtlassianClient) -> Dict[str, Any]:
    """Enhanced authentication test with comprehensive session details"""
    import time
    import requests
    from datetime import datetime

    result = {
        "timestamp": datetime.now().isoformat(),
        "test_type": "comprehensive_authentication",
        "configuration": {},
        "authentication": {},
        "connectivity": {},
        "session_details": {},
        "api_capabilities": {},
        "summary": {}
    }

    try:
        # 1. Configuration Details
        config = client.config
        result["configuration"] = {
            "confluence_url": config.get('CONFLUENCE_URL', 'Not configured'),
            "jira_url": config.get('JIRA_URL', 'Not configured'),
            "confluence_username": config.get('CONFLUENCE_USERNAME', 'Not configured'),
            "jira_username": config.get('JIRA_USERNAME', 'Not configured'),
            "auth_method": getattr(client, 'auth_method', 'Unknown'),
            "verify_ssl": getattr(client, 'verify_ssl', True),
            "timeout": getattr(client, 'timeout', 30)
        }

        # 2. Environment Variables Check
        env_vars = {
            "CONFLUENCE_BROWSER_COOKIE": bool(os.getenv('CONFLUENCE_BROWSER_COOKIE')),
            "JIRA_BROWSER_COOKIE": bool(os.getenv('JIRA_BROWSER_COOKIE')),
            "ATLASSIAN_AUTH_METHOD": os.getenv('ATLASSIAN_AUTH_METHOD', 'Not set'),
            "ATLASSIAN_VERIFY_SSL": os.getenv('ATLASSIAN_VERIFY_SSL', 'Not set'),
            "ATLASSIAN_TIMEOUT": os.getenv('ATLASSIAN_TIMEOUT', 'Not set')
        }
        result["configuration"]["environment_variables"] = env_vars

        # 3. Session Details
        session_info = {
            "session_object": str(type(client.session)),
            "session_cookies": {},
            "session_headers": dict(client.session.headers) if hasattr(client.session, 'headers') else {},
            "confluence_session_active": getattr(client, 'confluence_session_active', False),
            "jira_session_active": getattr(client, 'jira_session_active', False)
        }

        # Extract session cookies
        if hasattr(client.session, 'cookies'):
            for cookie in client.session.cookies:
                session_info["session_cookies"][cookie.name] = {
                    "domain": cookie.domain,
                    "path": cookie.path,
                    "secure": cookie.secure,
                    "expires": str(cookie.expires) if cookie.expires else None,
                    "value_length": len(cookie.value) if cookie.value else 0
                }

        result["session_details"] = session_info

        # 4. Basic Connectivity Test
        basic_test = client.test_connection()
        result["connectivity"]["basic_test"] = basic_test

        # 5. Enhanced Confluence Testing
        confluence_details = {
            "connection_status": basic_test.get('confluence', False),
            "authentication_method": "unknown",
            "api_endpoints": {},
            "session_validation": {}
        }

        if config.get('CONFLUENCE_URL'):
            try:
                # Test status endpoint
                status_url = f"{config['CONFLUENCE_URL']}/status"
                status_response = requests.get(status_url, verify=client.verify_ssl, timeout=10)
                confluence_details["api_endpoints"]["status"] = {
                    "status_code": status_response.status_code,
                    "accessible": status_response.status_code == 200
                }

                # Test REST API with current session
                api_url = f"{config['CONFLUENCE_URL']}/rest/api/space?limit=1"
                api_response = client.session.get(api_url, timeout=10)
                confluence_details["api_endpoints"]["rest_api"] = {
                    "status_code": api_response.status_code,
                    "accessible": api_response.status_code == 200,
                    "authentication_working": api_response.status_code == 200
                }

                if api_response.status_code == 200:
                    confluence_details["authentication_method"] = "session_based"
                    try:
                        api_data = api_response.json()
                        confluence_details["api_endpoints"]["rest_api"]["response_preview"] = {
                            "size": api_data.get('size', 0),
                            "results_count": len(api_data.get('results', []))
                        }
                    except:
                        pass

                # Check browser cookie usage
                browser_cookie = os.getenv('CONFLUENCE_BROWSER_COOKIE')
                if browser_cookie:
                    confluence_details["session_validation"]["browser_cookie"] = {
                        "configured": True,
                        "length": len(browser_cookie),
                        "preview": f"{browser_cookie[:10]}...{browser_cookie[-10:]}" if len(browser_cookie) > 20 else browser_cookie
                    }

            except Exception as e:
                confluence_details["error"] = str(e)

        result["authentication"]["confluence"] = confluence_details

        # 6. Enhanced Jira Testing
        jira_details = {
            "connection_status": basic_test.get('jira', False),
            "authentication_method": "unknown",
            "api_endpoints": {},
            "session_validation": {}
        }

        if config.get('JIRA_URL'):
            try:
                # Test status endpoint
                status_url = f"{config['JIRA_URL']}/status"
                status_response = requests.get(status_url, verify=client.verify_ssl, timeout=10)
                jira_details["api_endpoints"]["status"] = {
                    "status_code": status_response.status_code,
                    "accessible": status_response.status_code == 200
                }

                # Test REST API with current session
                api_url = f"{config['JIRA_URL']}/rest/api/2/serverInfo"
                api_response = client.session.get(api_url, timeout=10)
                jira_details["api_endpoints"]["rest_api"] = {
                    "status_code": api_response.status_code,
                    "accessible": api_response.status_code == 200,
                    "authentication_working": api_response.status_code == 200
                }

                if api_response.status_code == 200:
                    jira_details["authentication_method"] = "session_based"
                    try:
                        api_data = api_response.json()
                        jira_details["api_endpoints"]["rest_api"]["server_info"] = {
                            "server_title": api_data.get('serverTitle', 'Unknown'),
                            "version": api_data.get('version', 'Unknown')
                        }
                    except:
                        pass

                # Check browser cookie usage
                browser_cookie = os.getenv('JIRA_BROWSER_COOKIE')
                if browser_cookie:
                    jira_details["session_validation"]["browser_cookie"] = {
                        "configured": True,
                        "length": len(browser_cookie),
                        "preview": f"{browser_cookie[:10]}...{browser_cookie[-10:]}" if len(browser_cookie) > 20 else browser_cookie
                    }

            except Exception as e:
                jira_details["error"] = str(e)

        result["authentication"]["jira"] = jira_details

        # 7. API Capabilities Assessment
        capabilities = {
            "confluence": {
                "spaces_access": False,
                "content_search": False,
                "page_operations": False
            },
            "jira": {
                "projects_access": False,
                "issues_search": False,
                "issue_operations": False
            }
        }

        # Test Confluence capabilities
        if confluence_details["api_endpoints"].get("rest_api", {}).get("accessible"):
            try:
                spaces = client.confluence.get_spaces(limit=1)
                capabilities["confluence"]["spaces_access"] = True
                capabilities["confluence"]["spaces_count"] = len(spaces.get('results', []))
            except:
                pass

        # Test Jira capabilities
        if jira_details["api_endpoints"].get("rest_api", {}).get("accessible"):
            try:
                projects = client.jira.get_projects()
                capabilities["jira"]["projects_access"] = True
                capabilities["jira"]["projects_count"] = len(projects)
            except:
                pass

        result["api_capabilities"] = capabilities

        # 8. Summary
        confluence_working = confluence_details.get("api_endpoints", {}).get("rest_api", {}).get("accessible", False)
        jira_working = jira_details.get("api_endpoints", {}).get("rest_api", {}).get("accessible", False)

        result["summary"] = {
            "overall_status": "success" if (confluence_working or jira_working) else "partial" if basic_test.get('confluence') or basic_test.get('jira') else "failed",
            "confluence_status": "working" if confluence_working else "connection_only" if basic_test.get('confluence') else "failed",
            "jira_status": "working" if jira_working else "connection_only" if basic_test.get('jira') else "failed",
            "authentication_method": "session_based" if (confluence_working or jira_working) else "basic",
            "browser_cookies_used": bool(os.getenv('CONFLUENCE_BROWSER_COOKIE')) or bool(os.getenv('JIRA_BROWSER_COOKIE')),
            "recommendations": []
        }

        # Add recommendations
        if not confluence_working and config.get('CONFLUENCE_URL'):
            if not os.getenv('CONFLUENCE_BROWSER_COOKIE'):
                result["summary"]["recommendations"].append("Consider setting CONFLUENCE_BROWSER_COOKIE for 2FA bypass")
            else:
                result["summary"]["recommendations"].append("Confluence browser cookie may be expired - extract fresh cookie")

        if not jira_working and config.get('JIRA_URL'):
            if not os.getenv('JIRA_BROWSER_COOKIE'):
                result["summary"]["recommendations"].append("Consider setting JIRA_BROWSER_COOKIE if 2FA is enabled")

        if not result["summary"]["recommendations"]:
            result["summary"]["recommendations"].append("All systems operational")

    except Exception as e:
        result["error"] = str(e)
        result["summary"] = {
            "overall_status": "error",
            "error_message": str(e)
        }

    return result


# MCP Server instance
server = Server("atlassian-server")


@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available Atlassian resources"""
    resources = []
    
    try:
        client = get_client()
        
        # Test connectivity
        connection_status = client.test_connection()
        
        # Add system info resource
        resources.append(Resource(
            uri="atlassian://system/info",
            name="System Information",
            description="Atlassian system information and connectivity status",
            mimeType="application/json"
        ))
        
        if connection_status.get('confluence', False):
            # Add Confluence resources
            resources.extend([
                Resource(
                    uri="atlassian://confluence/spaces",
                    name="Confluence Spaces",
                    description="List of all accessible Confluence spaces",
                    mimeType="application/json"
                ),
                Resource(
                    uri="atlassian://confluence/user",
                    name="Current Confluence User",
                    description="Current user information in Confluence",
                    mimeType="application/json"
                )
            ])
            
            # Add space-specific resources
            try:
                spaces = client.confluence.get_spaces(limit=50)
                for space in spaces.get('results', []):
                    space_key = space['key']
                    resources.append(Resource(
                        uri=f"atlassian://confluence/spaces/{space_key}",
                        name=f"Space: {space['name']}",
                        description=f"Pages and content in {space['name']} space",
                        mimeType="application/json"
                    ))
                    
                    resources.append(Resource(
                        uri=f"atlassian://confluence/spaces/{space_key}/pages",
                        name=f"Pages in {space['name']}",
                        description=f"All pages in the {space['name']} space",
                        mimeType="application/json"
                    ))
            except Exception as e:
                logger.warning(f"Could not list Confluence spaces: {e}")
        
        if connection_status.get('jira', False):
            # Add Jira resources
            resources.extend([
                Resource(
                    uri="atlassian://jira/projects",
                    name="Jira Projects",
                    description="List of all accessible Jira projects",
                    mimeType="application/json"
                ),
                Resource(
                    uri="atlassian://jira/user",
                    name="Current Jira User",
                    description="Current user information in Jira",
                    mimeType="application/json"
                ),
                Resource(
                    uri="atlassian://jira/filters",
                    name="Jira Filters",
                    description="User's saved Jira filters",
                    mimeType="application/json"
                ),
                Resource(
                    uri="atlassian://jira/dashboards",
                    name="Jira Dashboards",
                    description="User's Jira dashboards",
                    mimeType="application/json"
                )
            ])
            
            # Add project-specific resources
            try:
                projects = client.jira.get_projects()
                for project in projects[:20]:  # Limit to first 20 projects
                    project_key = project['key']
                    resources.append(Resource(
                        uri=f"atlassian://jira/projects/{project_key}",
                        name=f"Project: {project['name']}",
                        description=f"Issues and details for {project['name']} project",
                        mimeType="application/json"
                    ))
                    
                    resources.append(Resource(
                        uri=f"atlassian://jira/projects/{project_key}/issues",
                        name=f"Issues in {project['name']}",
                        description=f"Recent issues in the {project['name']} project",
                        mimeType="application/json"
                    ))
            except Exception as e:
                logger.warning(f"Could not list Jira projects: {e}")
    
    except Exception as e:
        logger.error(f"Error listing resources: {e}")
        # Add error resource
        resources.append(Resource(
            uri="atlassian://error",
            name="Connection Error",
            description=f"Error connecting to Atlassian: {str(e)}",
            mimeType="text/plain"
        ))
    
    return resources


@server.read_resource()
async def read_resource(request: ReadResourceRequest) -> ReadResourceResult:
    """Get a specific Atlassian resource"""
    uri = str(request.params.uri)
    parsed = urlparse(uri)
    
    if parsed.scheme != "atlassian":
        raise ValueError(f"Unsupported URI scheme: {parsed.scheme}")
    
    try:
        client = get_client()
        path_parts = parsed.path.strip('/').split('/')
        
        if len(path_parts) >= 2 and path_parts[0] == "system" and path_parts[1] == "info":
            # System information
            system_info = client.get_system_info()
            connection_status = client.test_connection()
            
            content = {
                "system_info": system_info,
                "connection_status": connection_status,
                "timestamp": asyncio.get_event_loop().time()
            }
            
            return ReadResourceResult(
                contents=[
                    TextResourceContents(
                        type="text",
                        uri=uri,
                        text=json.dumps(content, indent=2)
                    )
                ]
            )
        
        elif path_parts[0] == "confluence":
            return await handle_confluence_resource(client, path_parts[1:], parsed.query)
        
        elif path_parts[0] == "jira":
            return await handle_jira_resource(client, path_parts[1:], parsed.query)
        
        elif path_parts[0] == "error":
            return ReadResourceResult(
                contents=[
                    TextResourceContents(
                        type="text",
                        uri=uri,
                        text="Error: Could not connect to Atlassian services. Please check your configuration."
                    )
                ]
            )

        else:
            raise ValueError(f"Unknown resource path: {parsed.path}")

    except Exception as e:
        logger.error(f"Error getting resource {uri}: {e}")
        return ReadResourceResult(
            contents=[
                TextResourceContents(
                    type="text",
                    uri=uri,
                    text=f"Error retrieving resource: {str(e)}"
                )
            ]
        )


async def handle_confluence_resource(client: ExtendedAtlassianClient,
                                   path_parts: List[str], query: str) -> ReadResourceResult:
    """Handle Confluence-specific resource requests"""
    
    if path_parts[0] == "spaces":
        if len(path_parts) == 1:
            # List all spaces
            spaces = client.confluence.get_spaces(limit=100)
            return ReadResourceResult(
                contents=[
                    TextResourceContents(
                        type="text",
                        uri=f"atlassian://confluence/spaces",
                        text=json.dumps(spaces, indent=2)
                    )
                ]
            )

        elif len(path_parts) == 2:
            # Specific space details
            space_key = path_parts[1]
            space = client.confluence.get_space(space_key, expand="description,homepage")
            return ReadResourceResult(
                contents=[
                    TextResourceContents(
                        type="text",
                        text=json.dumps(space, indent=2)
                    )
                ]
            )

        elif len(path_parts) == 3 and path_parts[2] == "pages":
            # Pages in a specific space
            space_key = path_parts[1]
            pages = client.confluence.get_pages(space_key=space_key, limit=50,
                                              expand="body.storage,version,ancestors")
            return ReadResourceResult(
                contents=[
                    TextResourceContents(
                        type="text",
                        text=json.dumps(pages, indent=2)
                    )
                ]
            )

    elif path_parts[0] == "user":
        # Current user information
        user = client.confluence.get_user_details()
        return ReadResourceResult(
            contents=[
                TextResourceContents(
                    type="text",
                    text=json.dumps(user, indent=2)
                )
            ]
        )
    
    raise ValueError(f"Unknown Confluence resource: {'/'.join(path_parts)}")


async def handle_jira_resource(client: ExtendedAtlassianClient,
                              path_parts: List[str], query: str) -> ReadResourceResult:
    """Handle Jira-specific resource requests"""
    
    if path_parts[0] == "projects":
        if len(path_parts) == 1:
            # List all projects
            projects = client.jira.get_projects(expand="description,lead")
            return ReadResourceResult(
                contents=[
                    TextResourceContents(
                        type="text",
                        text=json.dumps(projects, indent=2)
                    )
                ]
            )

        elif len(path_parts) == 2:
            # Specific project details
            project_key = path_parts[1]
            project = client.jira.get_project(project_key, expand="description,lead,issueTypes,versions")
            return ReadResourceResult(
                contents=[
                    TextResourceContents(
                        type="text",
                        text=json.dumps(project, indent=2)
                    )
                ]
            )

        elif len(path_parts) == 3 and path_parts[2] == "issues":
            # Recent issues in a specific project
            project_key = path_parts[1]
            jql = f'project = "{project_key}" ORDER BY updated DESC'
            issues = client.jira.search_issues(jql, max_results=25,
                                             fields=['summary', 'status', 'assignee', 'created', 'updated'])
            return ReadResourceResult(
                contents=[
                    TextResourceContents(
                        type="text",
                        text=json.dumps(issues, indent=2)
                    )
                ]
            )

    elif path_parts[0] == "user":
        # Current user information
        user = client.jira.get_current_user()
        return ReadResourceResult(
            contents=[
                TextResourceContents(
                    type="text",
                    text=json.dumps(user, indent=2)
                )
            ]
        )

    elif path_parts[0] == "filters":
        # User's saved filters
        filters = client.jira.get_filters()
        return ReadResourceResult(
            contents=[
                TextResourceContents(
                    type="text",
                    text=json.dumps(filters, indent=2)
                )
            ]
        )

    elif path_parts[0] == "dashboards":
        # User's dashboards
        dashboards = client.jira.get_dashboards()
        return ReadResourceResult(
            contents=[
                TextResourceContents(
                    type="text",
                    text=json.dumps(dashboards, indent=2)
                )
            ]
        )
    
    raise ValueError(f"Unknown Jira resource: {'/'.join(path_parts)}")


@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available Atlassian tools"""
    tools = []

    try:
        client = get_client()
        connection_status = client.test_connection()

        # System tools
        tools.extend([
            Tool(
                name="atlassian_test_connection",
                description="Comprehensive authentication test for Atlassian services including session details, cookie validation, API capabilities, and configuration analysis",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            ),
            Tool(
                name="atlassian_get_system_info",
                description="Get system information for both Confluence and Jira",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            )
        ])

        if connection_status.get('confluence', False):
            # Confluence tools
            tools.extend([
                Tool(
                    name="confluence_search",
                    description="Search Confluence content using CQL",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "cql": {
                                "type": "string",
                                "description": "CQL query string"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of results",
                                "default": 25
                            },
                            "expand": {
                                "type": "string",
                                "description": "Comma-separated list of properties to expand"
                            }
                        },
                        "required": ["cql"]
                    }
                ),
                Tool(
                    name="confluence_get_page",
                    description="Get a specific Confluence page by ID",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "page_id": {
                                "type": "string",
                                "description": "Page ID"
                            },
                            "expand": {
                                "type": "string",
                                "description": "Comma-separated list of properties to expand",
                                "default": "body.storage,version"
                            }
                        },
                        "required": ["page_id"]
                    }
                ),
                Tool(
                    name="confluence_create_page",
                    description="Create a new Confluence page",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "space_key": {
                                "type": "string",
                                "description": "Space key where to create the page"
                            },
                            "title": {
                                "type": "string",
                                "description": "Page title"
                            },
                            "content": {
                                "type": "string",
                                "description": "Page content in HTML format"
                            },
                            "parent_id": {
                                "type": "string",
                                "description": "Parent page ID (optional)"
                            }
                        },
                        "required": ["space_key", "title", "content"]
                    }
                ),
                Tool(
                    name="confluence_update_page",
                    description="Update an existing Confluence page",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "page_id": {
                                "type": "string",
                                "description": "Page ID to update"
                            },
                            "title": {
                                "type": "string",
                                "description": "New page title"
                            },
                            "content": {
                                "type": "string",
                                "description": "New page content in HTML format"
                            },
                            "version": {
                                "type": "integer",
                                "description": "Current version number of the page"
                            },
                            "minor_edit": {
                                "type": "boolean",
                                "description": "Whether this is a minor edit",
                                "default": False
                            }
                        },
                        "required": ["page_id", "title", "content", "version"]
                    }
                ),
                Tool(
                    name="confluence_get_spaces",
                    description="Get list of Confluence spaces",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of spaces to return",
                                "default": 50
                            },
                            "expand": {
                                "type": "string",
                                "description": "Comma-separated list of properties to expand"
                            }
                        },
                        "required": []
                    }
                ),
                Tool(
                    name="confluence_get_page_comments",
                    description="Get comments for a Confluence page",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "page_id": {
                                "type": "string",
                                "description": "Page ID"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of comments to return",
                                "default": 25
                            }
                        },
                        "required": ["page_id"]
                    }
                ),
                Tool(
                    name="confluence_add_comment",
                    description="Add a comment to a Confluence page",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "page_id": {
                                "type": "string",
                                "description": "Page ID"
                            },
                            "comment": {
                                "type": "string",
                                "description": "Comment content in HTML format"
                            }
                        },
                        "required": ["page_id", "comment"]
                    }
                )
            ])

        if connection_status.get('jira', False):
            # Jira tools
            tools.extend([
                Tool(
                    name="jira_search_issues",
                    description="Search Jira issues using JQL",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "jql": {
                                "type": "string",
                                "description": "JQL query string"
                            },
                            "max_results": {
                                "type": "integer",
                                "description": "Maximum number of results",
                                "default": 50
                            },
                            "fields": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of fields to include in response"
                            },
                            "expand": {
                                "type": "string",
                                "description": "Comma-separated list of properties to expand"
                            }
                        },
                        "required": ["jql"]
                    }
                ),
                Tool(
                    name="jira_get_issue",
                    description="Get a specific Jira issue by key",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "issue_key": {
                                "type": "string",
                                "description": "Issue key (e.g., 'PROJ-123')"
                            },
                            "fields": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of fields to include in response"
                            },
                            "expand": {
                                "type": "string",
                                "description": "Comma-separated list of properties to expand"
                            }
                        },
                        "required": ["issue_key"]
                    }
                ),
                Tool(
                    name="jira_create_issue",
                    description="Create a new Jira issue",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "project_key": {
                                "type": "string",
                                "description": "Project key"
                            },
                            "summary": {
                                "type": "string",
                                "description": "Issue summary/title"
                            },
                            "issue_type": {
                                "type": "string",
                                "description": "Issue type (e.g., 'Bug', 'Task', 'Story')"
                            },
                            "description": {
                                "type": "string",
                                "description": "Issue description"
                            },
                            "assignee": {
                                "type": "string",
                                "description": "Assignee username"
                            },
                            "priority": {
                                "type": "string",
                                "description": "Priority name"
                            },
                            "labels": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of labels"
                            }
                        },
                        "required": ["project_key", "summary", "issue_type"]
                    }
                ),
                Tool(
                    name="jira_update_issue",
                    description="Update an existing Jira issue",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "issue_key": {
                                "type": "string",
                                "description": "Issue key to update"
                            },
                            "fields": {
                                "type": "object",
                                "description": "Fields to update"
                            },
                            "update": {
                                "type": "object",
                                "description": "Update operations"
                            }
                        },
                        "required": ["issue_key"]
                    }
                ),
                Tool(
                    name="jira_add_comment",
                    description="Add a comment to a Jira issue",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "issue_key": {
                                "type": "string",
                                "description": "Issue key"
                            },
                            "comment": {
                                "type": "string",
                                "description": "Comment text"
                            },
                            "visibility": {
                                "type": "object",
                                "description": "Visibility restrictions"
                            }
                        },
                        "required": ["issue_key", "comment"]
                    }
                ),
                Tool(
                    name="jira_transition_issue",
                    description="Transition a Jira issue to a new status",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "issue_key": {
                                "type": "string",
                                "description": "Issue key"
                            },
                            "transition_id": {
                                "type": "string",
                                "description": "Transition ID"
                            },
                            "comment": {
                                "type": "string",
                                "description": "Optional comment for the transition"
                            },
                            "fields": {
                                "type": "object",
                                "description": "Additional fields to update during transition"
                            }
                        },
                        "required": ["issue_key", "transition_id"]
                    }
                ),
                Tool(
                    name="jira_get_projects",
                    description="Get list of Jira projects",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "expand": {
                                "type": "string",
                                "description": "Comma-separated list of properties to expand"
                            }
                        },
                        "required": []
                    }
                )
            ])

    except Exception as e:
        logger.error(f"Error listing tools: {e}")
        # Add error tool
        tools.append(Tool(
            name="atlassian_error",
            description=f"Error connecting to Atlassian: {str(e)}",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ))

    return tools


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list:
    """Execute an Atlassian tool"""
    try:
        client = get_client()
        tool_name = name
        args = arguments or {}

        # System tools
        if tool_name == "atlassian_test_connection":
            # Enhanced authentication test with comprehensive session details
            result = await enhanced_test_connection(client)
            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, indent=2)
                )
            ]

        elif tool_name == "atlassian_get_system_info":
            result = client.get_system_info()
            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, indent=2)
                )
            ]

        # Confluence tools
        elif tool_name == "confluence_search":
            cql = args["cql"]
            limit = args.get("limit", 25)
            expand = args.get("expand")

            result = client.confluence.search_content(cql, limit=limit, expand=expand)
            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, indent=2)
                )
            ]

        elif tool_name == "confluence_get_page":
            page_id = args["page_id"]
            expand = args.get("expand", "body.storage,version")

            result = client.confluence.get_page(page_id, expand=expand)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=json.dumps(result, indent=2)
                    )
                ]
            )

        elif tool_name == "confluence_create_page":
            space_key = args["space_key"]
            title = args["title"]
            content = args["content"]
            parent_id = args.get("parent_id")

            result = client.confluence.create_page(space_key, title, content, parent_id)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"Page created successfully: {result['title']} (ID: {result['id']})\n\n" +
                             json.dumps(result, indent=2)
                    )
                ]
            )

        elif tool_name == "confluence_update_page":
            page_id = args["page_id"]
            title = args["title"]
            content = args["content"]
            version = args["version"]
            minor_edit = args.get("minor_edit", False)

            result = client.confluence.update_page(page_id, title, content, version, minor_edit)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"Page updated successfully: {result['title']} (Version: {result['version']['number']})\n\n" +
                             json.dumps(result, indent=2)
                    )
                ]
            )

        elif tool_name == "confluence_get_spaces":
            limit = args.get("limit", 50)
            expand = args.get("expand")

            result = client.confluence.get_spaces(limit=limit, expand=expand)
            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, indent=2)
                )
            ]

        elif tool_name == "confluence_get_page_comments":
            page_id = args["page_id"]
            limit = args.get("limit", 25)

            result = client.confluence.get_page_comments(page_id, limit=limit)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=json.dumps(result, indent=2)
                    )
                ]
            )

        elif tool_name == "confluence_add_comment":
            page_id = args["page_id"]
            comment = args["comment"]

            result = client.confluence.add_page_comment(page_id, comment)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"Comment added successfully to page {page_id}\n\n" +
                             json.dumps(result, indent=2)
                    )
                ]
            )

        # Jira tools
        elif tool_name == "jira_search_issues":
            jql = args["jql"]
            max_results = args.get("max_results", 50)
            fields = args.get("fields")
            expand = args.get("expand")

            result = client.jira.search_issues(jql, max_results=max_results, fields=fields, expand=expand)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=json.dumps(result, indent=2)
                    )
                ]
            )

        elif tool_name == "jira_get_issue":
            issue_key = args["issue_key"]
            fields = args.get("fields")
            expand = args.get("expand")

            result = client.jira.get_issue(issue_key, fields=fields, expand=expand)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=json.dumps(result, indent=2)
                    )
                ]
            )

        elif tool_name == "jira_create_issue":
            project_key = args["project_key"]
            summary = args["summary"]
            issue_type = args["issue_type"]
            description = args.get("description")
            assignee = args.get("assignee")
            priority = args.get("priority")
            labels = args.get("labels")

            result = client.jira.create_issue(
                project_key, summary, issue_type, description,
                assignee, priority, labels
            )
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"Issue created successfully: {result['key']}\n\n" +
                             json.dumps(result, indent=2)
                    )
                ]
            )

        elif tool_name == "jira_update_issue":
            issue_key = args["issue_key"]
            fields = args.get("fields")
            update = args.get("update")

            result = client.jira.update_issue(issue_key, fields=fields, update=update)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"Issue {issue_key} updated successfully: {result}"
                    )
                ]
            )

        elif tool_name == "jira_add_comment":
            issue_key = args["issue_key"]
            comment = args["comment"]
            visibility = args.get("visibility")

            result = client.jira.add_comment(issue_key, comment, visibility)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"Comment added successfully to issue {issue_key}\n\n" +
                             json.dumps(result, indent=2)
                    )
                ]
            )

        elif tool_name == "jira_transition_issue":
            issue_key = args["issue_key"]
            transition_id = args["transition_id"]
            comment = args.get("comment")
            fields = args.get("fields")

            result = client.jira.transition_issue(issue_key, transition_id, comment, fields)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"Issue {issue_key} transitioned successfully: {result}"
                    )
                ]
            )

        elif tool_name == "jira_get_projects":
            expand = args.get("expand")

            result = client.jira.get_projects(expand=expand)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=json.dumps(result, indent=2)
                    )
                ]
            )

        elif tool_name == "atlassian_error":
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text="Error: Could not connect to Atlassian services. Please check your configuration."
                    )
                ]
            )

        else:
            raise ValueError(f"Unknown tool: {tool_name}")

    except Exception as e:
        logger.error(f"Error executing tool {tool_name}: {e}")
        return [
            TextContent(
                type="text",
                text=f"Error executing tool: {str(e)}"
            )
        ]


async def main():
    """Main entry point for the MCP server"""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="atlassian-server",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={}
                )
            )
        )


if __name__ == "__main__":
    asyncio.run(main())
