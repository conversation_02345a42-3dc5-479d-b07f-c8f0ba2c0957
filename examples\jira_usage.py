#!/usr/bin/env python3
"""
Examples of using Jira tools
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'mcp-server'))

from extended_atlassian_client import ExtendedAtlassianClient
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

def get_jira_projects():
    """Get list of Jira projects"""
    print("🎫 Getting Jira Projects")
    print("="*40)
    
    try:
        client = ExtendedAtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        projects = client.jira.get_projects()
        
        print(f"Found {len(projects)} projects:")
        for project in projects:
            print(f"  • {project['key']}: {project['name']}")
            if 'description' in project and project['description']:
                print(f"    Description: {project['description'][:100]}...")
            print()
        
        return projects
        
    except Exception as e:
        print(f"❌ Error getting projects: {e}")
        return []

def search_jira_issues(jql="ORDER BY updated DESC", max_results=10):
    """Search for Jira issues"""
    print(f"🔍 Searching Jira Issues")
    print(f"JQL: {jql}")
    print("="*40)
    
    try:
        client = ExtendedAtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        result = client.jira.search_issues(jql=jql, max_results=max_results)
        
        issues = result.get('issues', [])
        total = result.get('total', 0)
        
        print(f"Found {total} total issues (showing {len(issues)}):")
        
        for issue in issues:
            key = issue['key']
            summary = issue['fields']['summary']
            status = issue['fields']['status']['name']
            assignee = issue['fields'].get('assignee')
            assignee_name = assignee['displayName'] if assignee else 'Unassigned'
            
            print(f"  • {key}: {summary}")
            print(f"    Status: {status} | Assignee: {assignee_name}")
            print()
        
        return issues
        
    except Exception as e:
        print(f"❌ Error searching issues: {e}")
        return []

def get_specific_issue(issue_key):
    """Get details of a specific issue"""
    print(f"📋 Getting Issue Details: {issue_key}")
    print("="*40)
    
    try:
        client = ExtendedAtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        issue = client.jira.get_issue(issue_key)
        
        print(f"Key: {issue['key']}")
        print(f"Summary: {issue['fields']['summary']}")
        print(f"Status: {issue['fields']['status']['name']}")
        print(f"Priority: {issue['fields']['priority']['name']}")
        print(f"Issue Type: {issue['fields']['issuetype']['name']}")
        
        assignee = issue['fields'].get('assignee')
        if assignee:
            print(f"Assignee: {assignee['displayName']} ({assignee['emailAddress']})")
        else:
            print("Assignee: Unassigned")
        
        return issue
        
    except Exception as e:
        print(f"❌ Error getting issue {issue_key}: {e}")
        return None

if __name__ == "__main__":
    print("🎫 Jira Usage Examples")
    print("="*50)
    
    # Example 1: Get all projects
    projects = get_jira_projects()
    
    if projects:
        print("\n" + "="*50)
        
        # Example 2: Search recent issues
        search_jira_issues("updated >= -30d ORDER BY updated DESC", max_results=5)
        
        print("\n" + "="*50)
        
        # Example 3: Search issues in first project
        first_project = projects[0]['key']
        search_jira_issues(f"project = {first_project} ORDER BY updated DESC", max_results=3)
    
    print("\n🎉 Examples completed!")
