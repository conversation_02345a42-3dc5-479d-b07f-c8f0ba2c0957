"""
Atlassian Client Package

A comprehensive Python client for interacting with Atlassian on-premise installations
(Confluence and Jira) via their REST APIs.

Author: Generated for Smals.be integration
"""

from .atlassian_client import (
    AtlassianClient,
    ConfluenceAPI,
    JiraAPI,
    PaginationHelper,
    AtlassianUtils,
    AtlassianError,
    AuthenticationError,
    APIError
)

__version__ = "1.0.0"
__author__ = "Smals.be Integration Team"

__all__ = [
    'AtlassianClient',
    'ConfluenceAPI', 
    'JiraAPI',
    'PaginationHelper',
    'AtlassianUtils',
    'AtlassianError',
    'AuthenticationError',
    'APIError'
]
