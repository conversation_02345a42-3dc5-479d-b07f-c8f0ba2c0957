#!/usr/bin/env python3
"""
Example usage of the Atlassian Client for Smals.be

This script demonstrates how to use the AtlassianClient to interact with
both Confluence and Jira on-premise installations.
"""

import logging
import sys
import os

# Add parent directory to path to import client
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from client import AtlassianClient, ConfluenceAPI, JiraAPI, PaginationHelper, AtlassianUtils
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Use configuration from config.py
CONFIG = ATLASSIAN_CONFIG


def test_connection():
    """Test connectivity to both Confluence and Jira"""
    logger.info("Testing connection to Atlassian services...")
    
    try:
        client = AtlassianClient(CONFIG, **CLIENT_SETTINGS)
        results = client.test_connection()
        
        logger.info(f"Confluence connection: {'✓' if results['confluence'] else '✗'}")
        logger.info(f"Jira connection: {'✓' if results['jira'] else '✗'}")
        
        return results
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        return {'confluence': False, 'jira': False}


def confluence_examples():
    """Demonstrate Confluence API usage"""
    logger.info("=== Confluence API Examples ===")
    
    try:
        client = AtlassianClient(CONFIG, **CLIENT_SETTINGS)
        confluence = ConfluenceAPI(client)
        
        # Get all spaces
        logger.info("Fetching spaces...")
        spaces = confluence.get_spaces(limit=10)
        logger.info(f"Found {len(spaces.get('results', []))} spaces")
        
        for space in spaces.get('results', [])[:3]:  # Show first 3 spaces
            logger.info(f"  - {space['key']}: {space['name']}")
        
        # If we have spaces, work with the first one
        if spaces.get('results'):
            space_key = spaces['results'][0]['key']
            logger.info(f"\nWorking with space: {space_key}")
            
            # Get space details
            space_details = confluence.get_space(space_key, expand='description,homepage')
            logger.info(f"Space description: {space_details.get('description', {}).get('plain', {}).get('value', 'No description')[:100]}...")
            
            # Get pages in the space
            logger.info("Fetching pages in space...")
            pages = confluence.get_pages(space_key=space_key, limit=5, expand='body.storage,version')
            logger.info(f"Found {len(pages.get('results', []))} pages")
            
            for page in pages.get('results', [])[:3]:  # Show first 3 pages
                logger.info(f"  - {page['title']} (ID: {page['id']}, Version: {page['version']['number']})")
            
            # Search for content
            logger.info("\nSearching for content...")
            cql = AtlassianUtils.build_cql_query(space=space_key, content_type='page')
            search_results = confluence.search_content(cql, limit=5)
            logger.info(f"Search found {len(search_results.get('results', []))} results")
        
        # Example: Create a test page (commented out to avoid creating actual content)
        # logger.info("\nCreating a test page...")
        # new_page = confluence.create_page(
        #     space_key=space_key,
        #     title="Test Page from API",
        #     content="<p>This is a test page created via the API.</p>"
        # )
        # logger.info(f"Created page: {new_page['title']} (ID: {new_page['id']})")
        
    except Exception as e:
        logger.error(f"Confluence examples failed: {e}")


def jira_examples():
    """Demonstrate Jira API usage"""
    logger.info("\n=== Jira API Examples ===")
    
    try:
        client = AtlassianClient(CONFIG, **CLIENT_SETTINGS)
        jira = JiraAPI(client)
        
        # Get server info
        logger.info("Getting server information...")
        server_info = jira.get_server_info()
        logger.info(f"Jira version: {server_info.get('version', 'Unknown')}")
        logger.info(f"Server title: {server_info.get('serverTitle', 'Unknown')}")
        
        # Get all projects
        logger.info("\nFetching projects...")
        projects = jira.get_projects(expand='description,lead')
        logger.info(f"Found {len(projects)} projects")
        
        for project in projects[:3]:  # Show first 3 projects
            logger.info(f"  - {project['key']}: {project['name']}")
            if 'lead' in project:
                logger.info(f"    Lead: {project['lead'].get('displayName', 'Unknown')}")
        
        # If we have projects, work with the first one
        if projects:
            project_key = projects[0]['key']
            logger.info(f"\nWorking with project: {project_key}")
            
            # Get project details
            project_details = jira.get_project(project_key, expand='issueTypes,versions')
            logger.info(f"Project description: {project_details.get('description', 'No description')[:100]}...")
            
            # Get issue types for the project
            issue_types = jira.get_issue_types(project_key)
            logger.info(f"Available issue types: {[it['name'] for it in issue_types[:3]]}")
            
            # Search for issues in the project
            logger.info("\nSearching for issues...")
            jql = AtlassianUtils.build_jql_query(project=project_key)
            search_results = jira.search_issues(jql, max_results=5, 
                                              fields=['summary', 'status', 'assignee', 'created'])
            
            logger.info(f"Found {search_results.get('total', 0)} issues")
            for issue in search_results.get('issues', [])[:3]:  # Show first 3 issues
                logger.info(f"  - {issue['key']}: {issue['fields']['summary']}")
                logger.info(f"    Status: {issue['fields']['status']['name']}")
                assignee = issue['fields'].get('assignee')
                if assignee:
                    logger.info(f"    Assignee: {assignee['displayName']}")
                else:
                    logger.info("    Assignee: Unassigned")
        
        # Example: Create a test issue (commented out to avoid creating actual issues)
        # logger.info("\nCreating a test issue...")
        # new_issue = jira.create_issue(
        #     project_key=project_key,
        #     summary="Test Issue from API",
        #     issue_type="Task",
        #     description="This is a test issue created via the API."
        # )
        # logger.info(f"Created issue: {new_issue['key']}")
        
    except Exception as e:
        logger.error(f"Jira examples failed: {e}")


def pagination_example():
    """Demonstrate pagination helper usage"""
    logger.info("\n=== Pagination Example ===")
    
    try:
        client = AtlassianClient(CONFIG, **CLIENT_SETTINGS)
        confluence = ConfluenceAPI(client)
        
        # Get all spaces using pagination
        logger.info("Fetching ALL spaces using pagination...")
        all_spaces = list(PaginationHelper.get_all_pages(
            confluence.get_spaces, 
            limit=2  # Small limit to demonstrate pagination
        ))
        
        logger.info(f"Total spaces found: {len(all_spaces)}")
        for i, space in enumerate(all_spaces[:5], 1):  # Show first 5
            logger.info(f"  {i}. {space['key']}: {space['name']}")
        
        if len(all_spaces) > 5:
            logger.info(f"  ... and {len(all_spaces) - 5} more")
            
    except Exception as e:
        logger.error(f"Pagination example failed: {e}")


def utility_examples():
    """Demonstrate utility functions"""
    logger.info("\n=== Utility Examples ===")
    
    # CQL query building
    cql = AtlassianUtils.build_cql_query(
        space="DEMO",
        title="Getting Started",
        content_type="page"
    )
    logger.info(f"Built CQL query: {cql}")
    
    # JQL query building
    jql = AtlassianUtils.build_jql_query(
        project="PROJ",
        status="Open",
        assignee="john.doe",
        created_date="-1w"
    )
    logger.info(f"Built JQL query: {jql}")
    
    # Content formatting
    markdown_content = """
    # Hello World
    
    This is **bold** text and this is *italic*.
    
    Here's some `code`.
    """
    
    html_content = AtlassianUtils.format_confluence_content(markdown_content, 'markdown')
    logger.info(f"Converted markdown to HTML: {html_content[:100]}...")
    
    # URL parsing
    confluence_url = "https://confluence.smals.be/pages/viewpage.action?pageId=123456"
    page_id = AtlassianUtils.extract_page_id_from_url(confluence_url)
    logger.info(f"Extracted page ID: {page_id}")
    
    jira_url = "https://jira.smals.be/browse/PROJ-123"
    issue_key = AtlassianUtils.extract_issue_key_from_url(jira_url)
    logger.info(f"Extracted issue key: {issue_key}")


def main():
    """Main function to run all examples"""
    logger.info("Starting Atlassian Client Examples")
    logger.info("=" * 50)
    
    # Test connection first
    connection_results = test_connection()
    
    if connection_results['confluence']:
        confluence_examples()
        pagination_example()
    else:
        logger.warning("Skipping Confluence examples due to connection failure")
    
    if connection_results['jira']:
        jira_examples()
    else:
        logger.warning("Skipping Jira examples due to connection failure")
    
    # Utility examples don't require connection
    utility_examples()
    
    logger.info("\n" + "=" * 50)
    logger.info("Examples completed!")


if __name__ == "__main__":
    main()
