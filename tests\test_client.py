#!/usr/bin/env python3
"""
Test script for the Atlassian Client

This script validates the client functionality with the provided configuration
and provides diagnostic information for troubleshooting.
"""

import logging
import sys
import traceback
from datetime import datetime
import os

# Add parent directory to path to import client
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from client import (
    AtlassianClient, ConfluenceAPI, JiraAPI,
    AtlassianError, AuthenticationError, APIError,
    AtlassianUtils, PaginationHelper
)
from config import ATLASSIAN_CONFIG

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'atlassian_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

# Use configuration from config.py
CONFIG = ATLASSIAN_CONFIG


class TestResult:
    """Class to track test results"""
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.skipped = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        logger.info(f"PASS: {test_name}")

    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append((test_name, str(error)))
        logger.error(f"FAIL: {test_name}: {error}")

    def add_skip(self, test_name, reason):
        self.skipped += 1
        logger.warning(f"SKIP: {test_name}: {reason}")
    
    def summary(self):
        total = self.passed + self.failed + self.skipped
        logger.info(f"\n{'='*50}")
        logger.info(f"TEST SUMMARY")
        logger.info(f"{'='*50}")
        logger.info(f"Total tests: {total}")
        logger.info(f"Passed: {self.passed}")
        logger.info(f"Failed: {self.failed}")
        logger.info(f"Skipped: {self.skipped}")
        
        if self.errors:
            logger.info(f"\nFAILED TESTS:")
            for test_name, error in self.errors:
                logger.info(f"  - {test_name}: {error}")
        
        success_rate = (self.passed / total * 100) if total > 0 else 0
        logger.info(f"\nSuccess rate: {success_rate:.1f}%")
        
        return self.failed == 0


def test_client_initialization():
    """Test client initialization and configuration validation"""
    result = TestResult()
    
    # Test valid configuration
    try:
        client = AtlassianClient(CONFIG, auth_method='bearer')
        result.add_pass("Client initialization with valid config")
    except Exception as e:
        result.add_fail("Client initialization with valid config", e)
    
    # Test invalid configuration
    try:
        invalid_config = {}
        client = AtlassianClient(invalid_config)
        result.add_fail("Client initialization with invalid config", "Should have raised ValueError")
    except ValueError:
        result.add_pass("Client initialization with invalid config (correctly rejected)")
    except Exception as e:
        result.add_fail("Client initialization with invalid config", f"Unexpected error: {e}")
    
    # Test partial configuration
    try:
        partial_config = {
            "CONFLUENCE_URL": "https://confluence.smals.be",
            "CONFLUENCE_USERNAME": "<EMAIL>",
            "CONFLUENCE_API_TOKEN": "token"
        }
        client = AtlassianClient(partial_config)
        result.add_pass("Client initialization with partial config (Confluence only)")
    except Exception as e:
        result.add_fail("Client initialization with partial config", e)
    
    return result


def test_connection():
    """Test connectivity to Atlassian services"""
    result = TestResult()
    
    try:
        client = AtlassianClient(CONFIG, auth_method='bearer')
        connections = client.test_connection()
        
        if connections['confluence']:
            result.add_pass("Confluence connectivity")
        else:
            result.add_fail("Confluence connectivity", "Connection failed")
        
        if connections['jira']:
            result.add_pass("Jira connectivity")
        else:
            result.add_fail("Jira connectivity", "Connection failed")
        
        return result, connections
        
    except Exception as e:
        result.add_fail("Connection test", e)
        return result, {'confluence': False, 'jira': False}


def test_confluence_api(has_confluence):
    """Test Confluence API functionality"""
    result = TestResult()
    
    if not has_confluence:
        result.add_skip("All Confluence tests", "No Confluence connectivity")
        return result
    
    try:
        client = AtlassianClient(CONFIG, auth_method='bearer')
        confluence = ConfluenceAPI(client)
        
        # Test get spaces
        try:
            spaces = confluence.get_spaces(limit=5)
            if 'results' in spaces and len(spaces['results']) >= 0:
                result.add_pass("Get spaces")
                
                # Test get specific space if available
                if spaces['results']:
                    space_key = spaces['results'][0]['key']
                    try:
                        space = confluence.get_space(space_key)
                        result.add_pass("Get specific space")
                    except Exception as e:
                        result.add_fail("Get specific space", e)
                    
                    # Test get pages
                    try:
                        pages = confluence.get_pages(space_key=space_key, limit=3)
                        result.add_pass("Get pages from space")
                        
                        # Test get specific page if available
                        if pages.get('results'):
                            page_id = pages['results'][0]['id']
                            try:
                                page = confluence.get_page(page_id)
                                result.add_pass("Get specific page")
                            except Exception as e:
                                result.add_fail("Get specific page", e)
                    except Exception as e:
                        result.add_fail("Get pages from space", e)
            else:
                result.add_fail("Get spaces", "Invalid response format")
        except Exception as e:
            result.add_fail("Get spaces", e)
        
        # Test search functionality
        try:
            cql = AtlassianUtils.build_cql_query(content_type='page')
            search_results = confluence.search_content(cql, limit=3)
            result.add_pass("Search content with CQL")
        except Exception as e:
            result.add_fail("Search content with CQL", e)
        
    except Exception as e:
        result.add_fail("Confluence API initialization", e)
    
    return result


def test_jira_api(has_jira):
    """Test Jira API functionality"""
    result = TestResult()
    
    if not has_jira:
        result.add_skip("All Jira tests", "No Jira connectivity")
        return result
    
    try:
        client = AtlassianClient(CONFIG, auth_method='bearer')
        jira = JiraAPI(client)
        
        # Test server info
        try:
            server_info = jira.get_server_info()
            if 'version' in server_info:
                result.add_pass("Get server info")
            else:
                result.add_fail("Get server info", "Invalid response format")
        except Exception as e:
            result.add_fail("Get server info", e)
        
        # Test get projects
        try:
            projects = jira.get_projects()
            if isinstance(projects, list):
                result.add_pass("Get projects")
                
                # Test get specific project if available
                if projects:
                    project_key = projects[0]['key']
                    try:
                        project = jira.get_project(project_key)
                        result.add_pass("Get specific project")
                    except Exception as e:
                        result.add_fail("Get specific project", e)
                    
                    # Test get issue types
                    try:
                        issue_types = jira.get_issue_types(project_key)
                        result.add_pass("Get issue types")
                    except Exception as e:
                        result.add_fail("Get issue types", e)
                    
                    # Test search issues
                    try:
                        jql = AtlassianUtils.build_jql_query(project=project_key)
                        search_results = jira.search_issues(jql, max_results=3)
                        result.add_pass("Search issues with JQL")
                        
                        # Test get specific issue if available
                        if search_results.get('issues'):
                            issue_key = search_results['issues'][0]['key']
                            try:
                                issue = jira.get_issue(issue_key)
                                result.add_pass("Get specific issue")
                            except Exception as e:
                                result.add_fail("Get specific issue", e)
                    except Exception as e:
                        result.add_fail("Search issues with JQL", e)
            else:
                result.add_fail("Get projects", "Invalid response format")
        except Exception as e:
            result.add_fail("Get projects", e)
        
    except Exception as e:
        result.add_fail("Jira API initialization", e)
    
    return result


def test_utilities():
    """Test utility functions"""
    result = TestResult()
    
    # Test CQL query building
    try:
        cql = AtlassianUtils.build_cql_query(
            space='TEST',
            title='Sample',
            content_type='page'
        )
        if 'space = "TEST"' in cql and 'title ~ "Sample"' in cql:
            result.add_pass("CQL query building")
        else:
            result.add_fail("CQL query building", f"Unexpected query: {cql}")
    except Exception as e:
        result.add_fail("CQL query building", e)
    
    # Test JQL query building
    try:
        jql = AtlassianUtils.build_jql_query(
            project='PROJ',
            status='Open',
            assignee='user'
        )
        if 'project = "PROJ"' in jql and 'status = "Open"' in jql:
            result.add_pass("JQL query building")
        else:
            result.add_fail("JQL query building", f"Unexpected query: {jql}")
    except Exception as e:
        result.add_fail("JQL query building", e)
    
    # Test content formatting
    try:
        markdown = "# Title\n\nThis is **bold** text."
        html = AtlassianUtils.format_confluence_content(markdown, 'markdown')
        if '<strong>' in html and '<p>' in html:
            result.add_pass("Content formatting")
        else:
            result.add_fail("Content formatting", f"Unexpected output: {html}")
    except Exception as e:
        result.add_fail("Content formatting", e)
    
    # Test URL parsing
    try:
        confluence_url = "https://confluence.smals.be/pages/viewpage.action?pageId=123456"
        page_id = AtlassianUtils.extract_page_id_from_url(confluence_url)
        if page_id == "123456":
            result.add_pass("Confluence URL parsing")
        else:
            result.add_fail("Confluence URL parsing", f"Expected '123456', got '{page_id}'")
    except Exception as e:
        result.add_fail("Confluence URL parsing", e)
    
    try:
        jira_url = "https://jira.smals.be/browse/PROJ-123"
        issue_key = AtlassianUtils.extract_issue_key_from_url(jira_url)
        if issue_key == "PROJ-123":
            result.add_pass("Jira URL parsing")
        else:
            result.add_fail("Jira URL parsing", f"Expected 'PROJ-123', got '{issue_key}'")
    except Exception as e:
        result.add_fail("Jira URL parsing", e)
    
    return result


def test_error_handling():
    """Test error handling"""
    result = TestResult()
    
    try:
        client = AtlassianClient(CONFIG, auth_method='bearer')
        confluence = ConfluenceAPI(client)
        
        # Test handling of non-existent page
        try:
            page = confluence.get_page('nonexistent-page-id')
            result.add_fail("Error handling for non-existent page", "Should have raised an exception")
        except APIError as e:
            if e.status_code == 404:
                result.add_pass("Error handling for non-existent page (404)")
            else:
                result.add_pass(f"Error handling for non-existent page ({e.status_code})")
        except Exception as e:
            result.add_pass(f"Error handling for non-existent page (generic: {type(e).__name__})")
        
    except Exception as e:
        result.add_skip("Error handling tests", f"Could not initialize client: {e}")
    
    return result


def main():
    """Run all tests"""
    logger.info("Starting Atlassian Client Test Suite")
    logger.info("=" * 60)
    
    overall_result = TestResult()
    
    # Test 1: Client initialization
    logger.info("\n1. Testing client initialization...")
    init_result = test_client_initialization()
    overall_result.passed += init_result.passed
    overall_result.failed += init_result.failed
    overall_result.skipped += init_result.skipped
    overall_result.errors.extend(init_result.errors)
    
    # Test 2: Connectivity
    logger.info("\n2. Testing connectivity...")
    conn_result, connections = test_connection()
    overall_result.passed += conn_result.passed
    overall_result.failed += conn_result.failed
    overall_result.skipped += conn_result.skipped
    overall_result.errors.extend(conn_result.errors)
    
    # Test 3: Confluence API
    logger.info("\n3. Testing Confluence API...")
    conf_result = test_confluence_api(connections['confluence'])
    overall_result.passed += conf_result.passed
    overall_result.failed += conf_result.failed
    overall_result.skipped += conf_result.skipped
    overall_result.errors.extend(conf_result.errors)
    
    # Test 4: Jira API
    logger.info("\n4. Testing Jira API...")
    jira_result = test_jira_api(connections['jira'])
    overall_result.passed += jira_result.passed
    overall_result.failed += jira_result.failed
    overall_result.skipped += jira_result.skipped
    overall_result.errors.extend(jira_result.errors)
    
    # Test 5: Utilities
    logger.info("\n5. Testing utility functions...")
    util_result = test_utilities()
    overall_result.passed += util_result.passed
    overall_result.failed += util_result.failed
    overall_result.skipped += util_result.skipped
    overall_result.errors.extend(util_result.errors)
    
    # Test 6: Error handling
    logger.info("\n6. Testing error handling...")
    error_result = test_error_handling()
    overall_result.passed += error_result.passed
    overall_result.failed += error_result.failed
    overall_result.skipped += error_result.skipped
    overall_result.errors.extend(error_result.errors)
    
    # Final summary
    success = overall_result.summary()
    
    if success:
        logger.info("\nSUCCESS: All tests passed! The client is ready for use.")
    else:
        logger.info("\nWARNING: Some tests failed. Please review the errors above.")
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error during testing: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)
