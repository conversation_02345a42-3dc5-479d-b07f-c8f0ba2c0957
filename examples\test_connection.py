#!/usr/bin/env python3
"""
Simple connection test example
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'mcp-server'))

from extended_atlassian_client import ExtendedAtlassianClient
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

def test_connection():
    """Test connection to Atlassian services"""
    print("🔗 Testing Atlassian Connection")
    print("="*40)
    
    try:
        # Create client
        client = ExtendedAtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        
        # Test connection
        results = client.test_connection()
        
        print("Connection Results:")
        print(f"  Confluence: {'✅ Connected' if results.get('confluence') else '❌ Failed'}")
        print(f"  Jira: {'✅ Connected' if results.get('jira') else '❌ Failed'}")
        
        return results
        
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return {'confluence': False, 'jira': False}

if __name__ == "__main__":
    test_connection()
