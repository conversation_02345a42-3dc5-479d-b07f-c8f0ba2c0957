#!/usr/bin/env python3
"""
Complete integration test showing both <PERSON>ra and Confluence working
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'mcp-server'))

from extended_atlassian_client import ExtendedAtlassianClient
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

def test_complete_integration(confluence_cookie=None):
    """Test complete Atlassian integration"""
    print("🎯 Complete Atlassian Integration Test")
    print("="*60)
    
    # Set up Confluence browser cookie if provided
    if confluence_cookie:
        os.environ['CONFLUENCE_BROWSER_COOKIE'] = confluence_cookie
        print(f"✅ Using Confluence browser cookie: {confluence_cookie[:20]}...")
    
    try:
        # Create client
        client = ExtendedAtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        
        # Test connections
        print("\n1. 🔗 Connection Test")
        print("-" * 30)
        results = client.test_connection()
        confluence_connected = results.get('confluence', False)
        jira_connected = results.get('jira', False)
        
        print(f"   Confluence: {'✅ Connected' if confluence_connected else '❌ Failed'}")
        print(f"   Jira: {'✅ Connected' if jira_connected else '❌ Failed'}")
        
        # Test Jira functionality
        print("\n2. 🎫 Jira Integration Test")
        print("-" * 30)
        
        if jira_connected:
            # Get projects
            try:
                projects = client.jira.get_projects()
                print(f"   ✅ Projects: Found {len(projects)} projects")
                if projects:
                    for i, project in enumerate(projects[:3]):
                        print(f"      {i+1}. {project['key']}: {project['name']}")
                else:
                    print("      (No projects visible - may need permissions)")
            except Exception as e:
                print(f"   ❌ Projects error: {e}")
            
            # Search issues
            try:
                result = client.jira.search_issues(
                    jql="ORDER BY updated DESC", 
                    max_results=3
                )
                issues = result.get('issues', [])
                total = result.get('total', 0)
                print(f"   ✅ Issues: Found {total} total issues")
                if issues:
                    for i, issue in enumerate(issues):
                        key = issue['key']
                        summary = issue['fields']['summary']
                        print(f"      {i+1}. {key}: {summary[:40]}...")
                else:
                    print("      (No issues visible - may need permissions)")
            except Exception as e:
                print(f"   ❌ Issues error: {e}")
        else:
            print("   ⚠️ Jira not connected - skipping tests")
        
        # Test Confluence functionality
        print("\n3. 📄 Confluence Integration Test")
        print("-" * 30)
        
        if confluence_connected:
            # Get spaces
            try:
                spaces = client.confluence.get_spaces(limit=5)
                space_results = spaces.get('results', [])
                print(f"   ✅ Spaces: Found {len(space_results)} spaces")
                for i, space in enumerate(space_results[:3]):
                    print(f"      {i+1}. {space.get('key', 'N/A')}: {space.get('name', 'N/A')}")
                if len(space_results) > 3:
                    print(f"      ... and {len(space_results) - 3} more")
            except Exception as e:
                print(f"   ❌ Spaces error: {e}")
            
            # Search content
            try:
                search_result = client.confluence.search_content(
                    cql="type=page", 
                    limit=3
                )
                pages = search_result.get('results', [])
                print(f"   ✅ Content: Found {len(pages)} pages")
                for i, page in enumerate(pages):
                    title = page.get('title', 'No title')
                    print(f"      {i+1}. {title[:50]}...")
            except Exception as e:
                print(f"   ❌ Content search error: {e}")
        else:
            print("   ⚠️ Confluence not connected")
            if not confluence_cookie:
                print("   💡 Try with browser cookie:")
                print("      python examples/complete_integration_test.py <cookie_value>")
        
        # Summary
        print("\n4. 📊 Integration Summary")
        print("-" * 30)
        
        if jira_connected and confluence_connected:
            print("   🎉 FULL INTEGRATION: Both Jira and Confluence working!")
            print("   ✅ Ready for production use")
        elif jira_connected:
            print("   ✅ PARTIAL INTEGRATION: Jira fully functional")
            print("   ⚠️ Confluence needs browser cookie for full access")
        else:
            print("   ❌ INTEGRATION ISSUES: Check configuration")
        
        return jira_connected, confluence_connected
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False, False
    finally:
        # Clean up environment variable
        if confluence_cookie and 'CONFLUENCE_BROWSER_COOKIE' in os.environ:
            del os.environ['CONFLUENCE_BROWSER_COOKIE']

def show_usage_guide():
    """Show usage guide"""
    print("\n💡 Usage Guide")
    print("="*60)
    print()
    print("🎫 **Jira (Always Available):**")
    print("   - Full API access with session authentication")
    print("   - Projects, issues, search, create, update, comments")
    print("   - Works with 2FA out of the box")
    print()
    print("📄 **Confluence (Requires Browser Cookie):**")
    print("   - Extract JSESSION cookie from browser after login")
    print("   - Full API access with browser cookie")
    print("   - Spaces, pages, search, create, update")
    print()
    print("🔧 **How to Get Browser Cookie:**")
    print("   1. Login to https://confluence.smals.be")
    print("   2. Complete 2FA authentication")
    print("   3. F12 > Application > Cookies > CONFLUENCESESSIONID")
    print("   4. Copy cookie value")
    print("   5. Run: python examples/complete_integration_test.py <cookie>")
    print()
    print("🚀 **Production Usage:**")
    print("   - Set CONFLUENCE_BROWSER_COOKIE environment variable")
    print("   - Use normal client code - cookie used automatically")
    print("   - Monitor cookie expiration and refresh as needed")
    print()

if __name__ == "__main__":
    # Check if cookie provided
    confluence_cookie = sys.argv[1] if len(sys.argv) > 1 else None
    
    jira_ok, confluence_ok = test_complete_integration(confluence_cookie)
    
    show_usage_guide()
    
    print("🎯 **Final Status:**")
    print("="*60)
    print(f"✅ Jira Integration: {'WORKING' if jira_ok else 'FAILED'}")
    print(f"{'✅' if confluence_ok else '⚠️'} Confluence Integration: {'WORKING' if confluence_ok else 'NEEDS BROWSER COOKIE'}")
    print()
    
    if jira_ok and confluence_ok:
        print("🎉 **COMPLETE SUCCESS!** Both integrations working perfectly!")
    elif jira_ok:
        print("✅ **PARTIAL SUCCESS!** Jira working, Confluence needs browser cookie")
    else:
        print("❌ **SETUP NEEDED** - Check configuration and authentication")
