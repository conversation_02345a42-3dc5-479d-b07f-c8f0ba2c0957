# Extension Icons

This directory should contain the extension icons:

- `icon16.png` - 16x16 pixels
- `icon48.png` - 48x48 pixels  
- `icon128.png` - 128x128 pixels

For now, you can:

1. **Skip icons temporarily**: Remove the "icons" section from manifest.json
2. **Use placeholder icons**: Create simple PNG files with the required sizes
3. **Generate icons**: Use any icon generator or image editor

## Quick Fix: Remove Icons from Manifest

Edit `manifest.json` and remove these lines:
```json
"icons": {
  "16": "icons/icon16.png",
  "48": "icons/icon48.png", 
  "128": "icons/icon128.png"
},
```

The extension will work without custom icons (Chrome will use a default icon).
