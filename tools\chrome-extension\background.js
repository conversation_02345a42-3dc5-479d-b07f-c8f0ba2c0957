// Background script for Atlassian Cookie Extractor
// Handles extension lifecycle and background tasks

chrome.runtime.onInstalled.addListener(() => {
    console.log('Atlassian Cookie Extractor installed');
});

// Handle messages from popup or content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'extractCookie') {
        extractCookieForSite(request.url, request.cookieName)
            .then(cookie => sendResponse({ success: true, cookie }))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // Keep message channel open for async response
    }
});

async function extractCookieForSite(url, cookieName) {
    return new Promise((resolve, reject) => {
        chrome.cookies.get({
            url: url,
            name: cookieName
        }, (cookie) => {
            if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
            } else if (cookie) {
                resolve(cookie);
            } else {
                reject(new Error(`<PERSON>ie ${cookieName} not found for ${url}`));
            }
        });
    });
}
