# 🍪 Atlassian Cookie Extractor - Simplified Version

## 🎯 What It Does
- Extracts Confluence and Jira session cookies
- Downloads a `.bat` file to set environment variables
- Simple one-click setup for your Python scripts

## 🚀 Quick Installation

### Step 1: Open Chrome Extensions
1. Open Google Chrome
2. Go to `chrome://extensions/`
3. Enable **"Developer mode"** (toggle in top-right corner)

### Step 2: Load Extension
1. Click **"Load unpacked"** button
2. Navigate to and select the `tools/chrome-extension` folder
3. Click **"Select Folder"**

### Step 3: Verify Installation
- You should see "Atlassian Cookie Extractor" in your extensions list
- The extension icon should appear in your Chrome toolbar

## 🎯 How to Use

### Simple 3-Step Process:
1. **Login to Confluence/Jira** with 2FA authentication
2. **Click extension icon** and extract all cookies at once
3. **Copy cookies or download .bat file** to set environment variables

### Detailed Steps:
1. **Login to Services**: Go to https://confluence.smals.be and https://jira.smals.be, complete 2FA for both
2. **Click Extension**: Click the extension icon in Chrome toolbar
3. **Extract All Cookies**: Click "Extract All Cookies" button (gets both Confluence and Jira)
4. **Copy Individual Cookies**: Click the 📋 icon next to each cookie to copy to clipboard
5. **Download Setup**: Click "📜 Download .bat File" button for automatic environment setup
6. **Run Setup**: Double-click the downloaded `setup_atlassian_cookies.bat` file

## 🔧 What the .bat File Does

The downloaded `setup_atlassian_cookies.bat` file will:
- Set `CONFLUENCE_BROWSER_COOKIE` environment variable
- Set `JIRA_BROWSER_COOKIE` environment variable (if extracted)
- Display confirmation messages
- Provide instructions for testing

## 🎉 After Running the .bat File

You can immediately test your setup:

```bash
# Test Confluence integration
python examples/confluence_with_browser_cookie.py

# Test complete integration
python examples/complete_integration_test.py

# Test connection
python examples/test_connection.py
```

## 🔧 Troubleshooting

### "No cookie found" Error
- Make sure you're logged into Confluence/Jira first
- Complete 2FA authentication
- Refresh the page and try again

### Extension Not Loading
- Check that Developer mode is enabled in Chrome
- Make sure all files are in the chrome-extension folder
- Try reloading the extension

### .bat File Not Working
- Make sure you extracted cookies first
- Right-click the .bat file and "Run as administrator" if needed
- Check that the file downloaded completely

## 📋 Files Included

```
chrome-extension/
├── manifest.json     # Extension configuration
├── popup.html       # Extension popup interface
├── popup.js         # Simplified functionality
├── background.js    # Background script
├── content.js       # Content script
└── INSTALL.md       # This guide
```

## 🎯 Summary

This simplified extension:
- ✅ Extracts cookies with one click
- ✅ Downloads ready-to-run .bat file
- ✅ Sets environment variables automatically
- ✅ Works with your existing Python scripts

**Perfect for quick setup and daily use!** 🚀
