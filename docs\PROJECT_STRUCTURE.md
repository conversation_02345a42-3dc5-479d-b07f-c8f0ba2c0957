# Project Structure

This document describes the organization of the Atlassian Companion project.

## Directory Structure

```
atlassian-companion/
├── client/                     # Core Atlassian client library
│   ├── __init__.py            # Package initialization
│   └── atlassian_client.py    # Main client implementation
├── mcp-server/                # MCP (Model Context Protocol) server
│   ├── extended_atlassian_client.py  # Extended client for MCP
│   ├── server.py              # MCP server implementation
│   ├── test_mcp_server.py     # MCP server tests
│   ├── setup_mcp.py           # MCP setup utilities
│   ├── mcp_config_env.json    # Environment-based MCP config
│   ├── requirements.txt       # MCP-specific dependencies
│   └── docs/                  # MCP documentation
├── examples/                  # Usage examples
│   ├── example_usage.py       # General usage examples
│   ├── jira_usage.py          # Jira-specific examples
│   └── test_connection.py     # Connection testing example
├── tests/                     # Test suite
│   ├── test_client.py         # Client tests
│   ├── test_auth.py           # Authentication tests
│   └── setup_and_test.py      # Setup and testing utilities
├── tools/                     # Utility tools
│   └── debug_auth.py          # Authentication debugging
├── docs/                      # Documentation
│   └── PROJECT_STRUCTURE.md   # This file
├── config.py                  # Configuration file
├── main.py                    # Main entry point
├── setup.py                   # Package setup
├── requirements.txt           # Project dependencies
└── README.md                  # Project overview
```

## Key Components

### Core Client (`client/`)
- **atlassian_client.py**: Main client with authentication and API methods
- Supports both Confluence and Jira APIs
- Handles session-based authentication for 2FA scenarios

### MCP Server (`mcp-server/`)
- **server.py**: MCP server implementation
- **extended_atlassian_client.py**: Enhanced client with MCP-specific features
- Provides 16 tools for Atlassian integration
- Supports both environment and file-based configuration

### Examples (`examples/`)
- **jira_usage.py**: How to use Jira tools (projects, issues, search)
- **test_connection.py**: Simple connection testing
- **example_usage.py**: General usage patterns

### Configuration
- **config.py**: Main configuration file with credentials
- **mcp_config_env.json**: Environment-based MCP configuration
- Supports session-based authentication for 2FA environments

## Usage

### Quick Start
```bash
# Test connection
python main.py test

# Run examples
python examples/test_connection.py
python examples/jira_usage.py

# Start MCP server
python mcp-server/server.py
```

### Authentication
The project supports session-based authentication which works with 2FA:
- Confluence: Session authentication working
- Jira: Status endpoint accessible, full API may require additional setup

### Current Status
- ✅ Jira integration fully working
- ✅ Session authentication implemented
- ✅ MCP server operational (7/8 tests passing)
- ⚠️ Confluence REST API blocked (server-side policy)
