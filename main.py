#!/usr/bin/env python3
"""
Main entry point for the Atlassian Client

This script provides a simple command-line interface to interact with
your Atlassian on-premise installations.
"""

import sys
import argparse
import logging
from client import AtlassianClient, ConfluenceAPI, <PERSON>raAP<PERSON>
from config import ATLASSIAN_CONFIG, CLIENT_SETTINGS

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_connection():
    """Test connectivity to both Confluence and Jira"""
    print("Testing connection to Atlassian services...")
    
    try:
        client = AtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        results = client.test_connection()
        
        print(f"Confluence: {'Connected' if results['confluence'] else 'Failed'}")
        print(f"Jira: {'Connected' if results['jira'] else 'Failed'}")
        
        return results['confluence'] and results['jira']
    except Exception as e:
        print(f"Connection test failed: {e}")
        return False


def list_confluence_spaces():
    """List Confluence spaces"""
    try:
        client = AtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        confluence = ConfluenceAPI(client)
        
        spaces = confluence.get_spaces(limit=20)
        
        print(f"\nFound {len(spaces.get('results', []))} Confluence spaces:")
        print("-" * 60)
        
        for space in spaces.get('results', []):
            print(f"Key: {space['key']:<15} Name: {space['name']}")
            
    except Exception as e:
        print(f"Failed to list Confluence spaces: {e}")


def list_jira_projects():
    """List Jira projects"""
    try:
        client = AtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        jira = JiraAPI(client)
        
        projects = jira.get_projects()
        
        print(f"\nFound {len(projects)} Jira projects:")
        print("-" * 60)
        
        # Show first 20 projects
        for project in projects[:20]:
            print(f"Key: {project['key']:<15} Name: {project['name']}")
            
        if len(projects) > 20:
            print(f"... and {len(projects) - 20} more projects")
            
    except Exception as e:
        print(f"Failed to list Jira projects: {e}")


def search_confluence(query):
    """Search Confluence content"""
    try:
        client = AtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        confluence = ConfluenceAPI(client)
        
        # Build CQL query
        cql = f'text ~ "{query}"'
        results = confluence.search_content(cql, limit=10)
        
        print(f"\nConfluence search results for '{query}':")
        print("-" * 60)
        
        for result in results.get('results', []):
            print(f"Title: {result['title']}")
            print(f"Type: {result['type']}")
            print(f"URL: {result['_links']['webui']}")
            print()
            
    except Exception as e:
        print(f"Failed to search Confluence: {e}")


def search_jira(query):
    """Search Jira issues"""
    try:
        client = AtlassianClient(ATLASSIAN_CONFIG, **CLIENT_SETTINGS)
        jira = JiraAPI(client)
        
        # Build JQL query
        jql = f'text ~ "{query}"'
        results = jira.search_issues(jql, max_results=10)
        
        print(f"\nJira search results for '{query}':")
        print("-" * 60)
        
        for issue in results.get('issues', []):
            print(f"Key: {issue['key']}")
            print(f"Summary: {issue['fields']['summary']}")
            print(f"Status: {issue['fields']['status']['name']}")
            print()
            
    except Exception as e:
        print(f"Failed to search Jira: {e}")


def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(description='Atlassian Client CLI')
    parser.add_argument('command', choices=[
        'test', 'spaces', 'projects', 'search-confluence', 'search-jira'
    ], help='Command to execute')
    parser.add_argument('--query', '-q', help='Search query (for search commands)')
    
    args = parser.parse_args()
    
    print("Atlassian Client")
    print("=" * 40)
    
    if args.command == 'test':
        success = test_connection()
        sys.exit(0 if success else 1)
        
    elif args.command == 'spaces':
        list_confluence_spaces()
        
    elif args.command == 'projects':
        list_jira_projects()
        
    elif args.command == 'search-confluence':
        if not args.query:
            print("Error: --query is required for search-confluence command")
            sys.exit(1)
        search_confluence(args.query)
        
    elif args.command == 'search-jira':
        if not args.query:
            print("Error: --query is required for search-jira command")
            sys.exit(1)
        search_jira(args.query)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
